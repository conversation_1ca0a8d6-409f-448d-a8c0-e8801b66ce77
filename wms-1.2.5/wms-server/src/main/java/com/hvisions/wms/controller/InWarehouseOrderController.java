package com.hvisions.wms.controller;

import com.hvisions.wms.dto.inwarehouseorder.InStoreDTO;
import com.hvisions.wms.dto.inwarehouseorder.InWarehouseOrderDetailDTO;
import com.hvisions.wms.dto.inwarehouseorder.InWarehouseOrderHeaderDTO;
import com.hvisions.wms.dto.inwarehouseorder.OrderQuery;
import com.hvisions.wms.entity.HvWmsInWarehouseOrderDetail;
import com.hvisions.wms.service.InWarehouseOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: InWarehouseOrderController</p>
 * <p>Description: 入库单控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping("/inWarehouseOrder")
@Api(description = "入库单控制器")
public class InWarehouseOrderController {

    private final InWarehouseOrderService service;

    @Autowired
    public InWarehouseOrderController(InWarehouseOrderService service) {
        this.service = service;
    }

    /**
     * 分页查询
     *
     * @param query 查询条件
     * @return 入库单信息
     */
    @PostMapping("/query")
    @ApiOperation("分页查询接口")
    public Page<InWarehouseOrderHeaderDTO> query(@RequestBody OrderQuery query) {
        return service.query(query);
    }


    /**
     * 入库单id
     *
     * @param id 主键
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除入库单")
    public void delete(@PathVariable Integer id) {
       service.delete(id);
    }

    /**
     * 根据id查询单据
     *
     * @param id 头表id
     * @return 单据信息
     */
    @GetMapping("/getById/{id}")
    @ApiOperation("根据id查询单据")
    public InWarehouseOrderHeaderDTO getById(@PathVariable Integer id) {
        return service.getById(id);
    }

    /**
     * 创建手动入库单
     *
     * @return 入库单信息
     */
    @PostMapping("/createOrder")
    @ApiOperation("创建手动入库单")
    public InWarehouseOrderHeaderDTO createOrder() {
        return service.createOrder();
    }

    /**
     * 根据采购单创建入库单
     *
     * @param purchaseOrderNum 入库单号
     * @return 入库单
     */
    @PostMapping("/createOrderByPurchaseOrder")
    @ApiOperation("根据采购单创建入库单")
    public InWarehouseOrderHeaderDTO createOrderByPurchaseOrder(@RequestParam String purchaseOrderNum) {
        return service.createOrderByPurchaseOrder(purchaseOrderNum);
    }

    /**
     * 完成入库
     *
     * @param id 入库单id
     */
    @PutMapping("/inStore/{id}")
    @ApiOperation("完成入库")
    public void inStore(@PathVariable Integer id) {
        service.inStore(id);
    }

    /**
     * 保存采购单
     *
     * @param headerDTO 头表信息
     */
    @PutMapping("/save")
    @ApiOperation("保存/只保存头表信息")
    public Integer save(@RequestBody InWarehouseOrderHeaderDTO headerDTO) {
        return service.save(headerDTO);
    }


    /**
     * 手动添加入库信息
     *
     * @param dto 入库信息
     */
    @PostMapping("/addStoreManual")
    @ApiOperation("手动添加入库信息")
    public Integer addStoreManual(@RequestBody @Valid InStoreDTO dto) {
       return service.addStoreManual(dto);
    }

    /**
     * 采购单添加入库信息
     *
     * @param dto 入库信息
     */
    @PostMapping("/addStoreByMaterial")
    @ApiOperation("采购单添加入库信息")
    public void addStoreByMaterial(@RequestBody @Valid InStoreDTO dto) {
        service.addStoreByMaterial(dto);
    }

    /**
     * 删除行信息
     *
     * @param id 行id
     */
    @DeleteMapping("/deleteLine/{id}")
    @ApiOperation("删除入库单行信息")
    public void deleteLine(@PathVariable Integer id) {
        service.deleteLine(id);
    }

    /**
     * 删除明细信息
     *
     * @param id 明细id
     */
    @DeleteMapping("/deleteDetail/{id}")
    @ApiOperation("删除入库单行信息")
    public void deleteDetail(@PathVariable Integer id) {
        service.deleteDetail(id);
    }

    @GetMapping("/getDetail/{lineId}")
    @ApiOperation("根据行id查询明细信息")
    public List<InWarehouseOrderDetailDTO> getDetailByLineId(@PathVariable Integer lineId){
        return service.getDetailByLineId(lineId);
    }
}









