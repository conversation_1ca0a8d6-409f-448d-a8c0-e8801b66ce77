package com.hvisions.wms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.agvScheduling.*;
import com.hvisions.wms.entity.agvScheduling.HvWmAgvScheduling;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface HvWmAgvSchedulingService extends IService<HvWmAgvScheduling> {
    Page<HvWmAgvSchedulingShowDTO> pageList(HvWmAgvSchedulingQueryDTO hvPmAgvSchedulingQueryDTO);
    List<HvWmAgvScheduling> findListByCondition(HvWmAgvSchedulingQueryDTO hvPmAgvSchedulingQueryDTO);
    ResultVO agvTaskDispatch(@RequestBody HvWmAgvSchedulingDTO agvSchedulingDTO);
    boolean isExistAgvSchedulingByRequestTaskNo(String requestTaskNo);
	Page<MaterialBoxStatDTO> statistics(MaterialBoxStatQueryDTO queryDTO);
}
