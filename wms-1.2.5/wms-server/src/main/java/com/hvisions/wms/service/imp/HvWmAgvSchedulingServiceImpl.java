package com.hvisions.wms.service.imp;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.thirdparty.common.dto.RCSSendTaskDTO;
import com.hvisions.thirdparty.common.dto.RCSTargetRouteDTO;
import com.hvisions.thridparty.client.RCSClient;
import com.hvisions.wms.dao.HvWmAgvSchedulingMapper;
import com.hvisions.wms.dto.agvScheduling.*;
import com.hvisions.wms.entity.agvScheduling.HvWmAgvScheduling;
import com.hvisions.wms.service.HvWmAgvSchedulingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class HvWmAgvSchedulingServiceImpl extends ServiceImpl<HvWmAgvSchedulingMapper, HvWmAgvScheduling>
        implements HvWmAgvSchedulingService {

    @Autowired
    private HvWmAgvSchedulingMapper hvWmAgvSchedulingMapper;
    @Autowired
    private RCSClient rcsClient;

    @Override
    public Page<HvWmAgvSchedulingShowDTO> pageList(HvWmAgvSchedulingQueryDTO hvPmAgvSchedulingQueryDTO) {
        return PageHelperUtil.getPage(hvWmAgvSchedulingMapper::getPage, hvPmAgvSchedulingQueryDTO);
    }

    @Override
    public List<HvWmAgvScheduling> findListByCondition(HvWmAgvSchedulingQueryDTO hvPmAgvSchedulingQueryDTO) {
        return hvWmAgvSchedulingMapper.findListByCondition(hvPmAgvSchedulingQueryDTO);
    }

    @Override
    public ResultVO agvTaskDispatch(HvWmAgvSchedulingDTO agvSchedulingDTO) {
        RCSSendTaskDTO rcsSendTaskDTO = new RCSSendTaskDTO();
//        任务编码
        rcsSendTaskDTO.setRobotTaskCode(agvSchedulingDTO.getTaskNo());
//        任务类型
//        rcsSendTaskDTO.setTaskType("MOM"+ agvSchedulingDTO.getTaskType());
        rcsSendTaskDTO.setTaskType("MOM02");
        List<RCSTargetRouteDTO> targetRoute = new ArrayList<>();
        RCSTargetRouteDTO targetRouteStart = new RCSTargetRouteDTO();
//        起点
        targetRouteStart.setSeq(0);
        targetRouteStart.setCode(agvSchedulingDTO.getStartPoint());
        targetRouteStart.setType("SITE");
        targetRoute.add(targetRouteStart);
//        终点
        RCSTargetRouteDTO targetRouteEnd = new RCSTargetRouteDTO();
        targetRouteStart.setSeq(1);
        targetRouteEnd.setCode(agvSchedulingDTO.getEndPoint());
        targetRouteEnd.setType("SITE");
        targetRoute.add(targetRouteEnd);
        rcsSendTaskDTO.setTargetRoute(targetRoute);
//        优先级
        rcsSendTaskDTO.setInitPriority(Integer.valueOf(agvSchedulingDTO.getPriority()));
//        AGV类型编号
        rcsSendTaskDTO.setRobotType(agvSchedulingDTO.getAgvTypeNo());
        return rcsClient.submitTask(rcsSendTaskDTO);
    }

    @Override
    public Page<MaterialBoxStatDTO> statistics(MaterialBoxStatQueryDTO queryDTO) {
        // 使用PageHelperUtil进行分页查询，支持排序
        return PageHelperUtil.getPage(hvWmAgvSchedulingMapper::statMaterialBox, queryDTO);
    }

    @Override
    public boolean isExistAgvSchedulingByRequestTaskNo(String requestTaskNo) {
        HvWmAgvScheduling hvWmAgvScheduling = hvWmAgvSchedulingMapper.selectOne(new LambdaQueryWrapper<HvWmAgvScheduling>().eq(HvWmAgvScheduling::getRequestTaskNo, requestTaskNo));
        if (hvWmAgvScheduling != null) {
            return true;
        }
        return false;
    }

}