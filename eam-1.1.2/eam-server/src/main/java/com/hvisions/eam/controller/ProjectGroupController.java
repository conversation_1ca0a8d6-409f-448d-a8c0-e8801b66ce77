package com.hvisions.eam.controller;

import com.hvisions.eam.dto.autonomy.ProjectGroupCreateOrUpdateDTO;
import com.hvisions.eam.dto.autonomy.ProjectGroupDTO;
import com.hvisions.eam.service.autonomy.ProjectGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-16
 */
@Api(tags = "项目组")
@RestController
@RequestMapping("/projectGroup")
public class ProjectGroupController {

    private final ProjectGroupService projectGroupService;

    @Autowired
    public ProjectGroupController(ProjectGroupService projectGroupService) {
        this.projectGroupService = projectGroupService;
    }

    /**
     * 新增
     * <AUTHOR>
     * @param dto 新增信息
     * @return int 主键
     */
    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public int add(@RequestBody @Valid ProjectGroupCreateOrUpdateDTO dto){
        return projectGroupService.add(dto);
    }

    /**
     * 删除单个
     * <AUTHOR>

     * @param id 主键
     */
    @ApiOperation(value = "删除单个")
    @DeleteMapping("/deleteById/{id}")
    public void deleteById(@PathVariable Integer id) {
        projectGroupService.deleteById(id);
    }

    /**
     * 修改
     * <AUTHOR>

     * @param dto 修改信息
     * @return int 主键
     */
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public int update(@RequestBody @Valid ProjectGroupCreateOrUpdateDTO dto){
        return projectGroupService.update(dto);
    }
    
    /**
     * 查询树形结构
     * <AUTHOR>
     * @return 树形结构
     */
    @ApiOperation(value = "查询树形结构")
    @PostMapping("/queryList")
    public List<ProjectGroupDTO> queryList() {
        return projectGroupService.queryList();
    }
}

