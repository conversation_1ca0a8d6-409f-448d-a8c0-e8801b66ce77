package com.hvisions.eam.controller;

import com.hvisions.eam.dto.publicstore.UseListingLubDTO;
import com.hvisions.eam.dto.publicstore.UseListingSpareDTO;
import com.hvisions.eam.query.publicstore.UseListingQueryDTO;
import com.hvisions.eam.service.publicstore.UseListingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title:UseListingController</p>
 * <p>Description:备件使用清单</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@RequestMapping(value = "/UseListingController")
@Api(description = "备件使用清单")
@Slf4j
public class UseListingController {

    /**
     * 清单service
     */
    private final UseListingService useListingService;

    @Autowired
    public UseListingController(UseListingService useListingService) {
        this.useListingService = useListingService;
    }

    /**
     * 增加 使用清单
     *
     * @param useListingSpareDTO 清单
     * @return id
     */
    @ApiOperation(value = "添加清单")
    @PostMapping(value = "/create")
    public Integer create(@RequestBody UseListingSpareDTO useListingSpareDTO) {
        return useListingService.create(useListingSpareDTO);
    }

    /**
     * 批量增加 使用清单
     *
     * @param useListingSpareDTOS 清单List
     * @return id
     */
    @ApiOperation(value = "批量添加清单")
    @PostMapping(value = "/createList")
    public List<Integer> createList(@RequestBody List<UseListingSpareDTO> useListingSpareDTOS) {
        log.info(useListingSpareDTOS.toString());
        return useListingService.createList(useListingSpareDTOS);
    }

    /**
     * 获取备件与processInstanceId关联的清单
     *
     * @param useListingQueryDTO 清单
     * @return 关联清单
     */
    @ApiOperation(value = "获取备件与processInstanceId关联的清单")
    @PostMapping(value = "/getSpareByProcessInstanceId")
    public Page<UseListingSpareDTO> getSpareByProcessInstanceId(@RequestBody UseListingQueryDTO useListingQueryDTO) {
         return useListingService.getSpareByProcessInstanceId(useListingQueryDTO);
    }

    /**
     * 获取油品与processInstanceId关联的清单
     *
     * @param useListingQueryDTO 清单
     * @return 关联清单
     */
    @ApiOperation(value = "获取油品与processInstanceId关联的清单")
    @PostMapping(value = "/getLubByProcessInstanceId")
    public Page<UseListingLubDTO> getLubByProcessInstanceId(@RequestBody UseListingQueryDTO useListingQueryDTO) {
        return useListingService.getLubByProcessInstanceId(useListingQueryDTO);
    }
}
