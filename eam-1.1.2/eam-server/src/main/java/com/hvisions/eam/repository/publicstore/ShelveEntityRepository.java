package com.hvisions.eam.repository.publicstore;

import com.hvisions.eam.entity.publicstore.HvEamShelve;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title:ShelveEntityRepository</p>
 * <p>Description:库房</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface ShelveEntityRepository extends JpaRepository<HvEamShelve, Integer> {

    List<HvEamShelve> findAllByShelveCodeIn(List<String> codes);
}
