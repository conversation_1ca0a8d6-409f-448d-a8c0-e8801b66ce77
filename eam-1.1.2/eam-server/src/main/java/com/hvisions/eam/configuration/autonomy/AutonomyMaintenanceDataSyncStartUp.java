package com.hvisions.eam.configuration.autonomy;

import com.hvisions.eam.service.autonomy.AutonomyMaintenanceDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: InitialStartup</p >
 * <p>Description:用于同步所有工作流中没有同步的设备保养数据
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
@Slf4j
public class AutonomyMaintenanceDataSyncStartUp implements CommandLineRunner {

    @Autowired
    AutonomyMaintenanceDataService autonomyMaintenanceDataService;

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Value("${h-visions.maintain.activiti-database:activiti}")
    String activitiDatabase;

    @Value(value = "${spring.datasource.url}")
    private String url;

    private final String mysql = ":mysql:";

    private final String sqlserver = ":sqlserver:";

    @Override
    public void run(String... args) {
        try {
            String sql = "";
            if (url.contains(mysql)) {
                sql = "select id_ from " + activitiDatabase + ".ACT_HI_PROCINST t1 " +
                        "where t1.PROC_DEF_ID_ like 'autonomymaintenance%' " +
                        "and t1.END_TIME_ is not null " +
                        "and not EXISTS (select 1 from hv_am_autonomy_maintenance_process_data t2 where t2.process_instance_id = t1.id_)";
            } else if (url.contains(sqlserver)) {
                sql = "select id_ from " + activitiDatabase + ".dbo" + ".ACT_HI_PROCINST t1 " +
                        "where t1.PROC_DEF_ID_ like 'autonomymaintenance%' " +
                        "and t1.END_TIME_ is not null " +
                        "and not EXISTS (select 1 from hv_am_autonomy_maintenance_process_data t2 where t2" +
                        ".process_instance_id = t1.id_)";
            }
            List<String> processlist = jdbcTemplate.queryForList(sql, String.class);
            for (String process : processlist) {
                autonomyMaintenanceDataService.save(process, false);
            }
        } catch (Exception ex) {
            log.error("同步数据异常", ex);
        }
    }
}