package com.hvisions.eam.repository.inspect;

import com.hvisions.eam.entity.inspect.HvEamInspectItemFile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: InspectItemFileRepository</p >
 * <p>Description: 点检项目文件关系Repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/19</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface InspectItemFileRepository extends JpaRepository<HvEamInspectItemFile, Integer> {
    /**
     * 根据项目id删除关系文件
     *
     * @param itemId 点检项目id
     */
    void deleteAllByInspectItemId(Integer itemId);


    /**
     * 根据项目id查询绑定的文件
     *
     * @param itemIds 点检项目id列表
     * @return 点检项目与文件关系实体集合
     */
    List<HvEamInspectItemFile> findAllByInspectItemIdIn(List<Integer> itemIds);

    /**
     * 根据项目id查询绑定的文件
     *
     * @param itemId 点检项目id列表
     * @return 点检项目与文件关系实体集合
     */
    List<HvEamInspectItemFile> findAllByInspectItemId(Integer itemId);
}