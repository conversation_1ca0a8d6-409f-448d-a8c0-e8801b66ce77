package com.hvisions.eam.configuration.inspect;

import com.hvisions.eam.service.inspect.InspectDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.api.RabbitListenerErrorHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: TopicRabbitConfig</p>
 * <p>Description:绑定工作流的流程结束事件处理业务 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Configuration
@Slf4j
public class InspectQueueConfig {
    @Autowired
    InspectDataService service;
    /**
     * timer-exchange交换机名称
     */
    private final static String MAINTAIN_PROCESS_FINISH_QUEUE = "h-visions.eam.inspection.process.finish";
    /**
     * 设备保养流程定义key
     */
    private final static String ROUTING_KEY = "equipment-inspect.*";

    /**
     * 监听队列,当流程结束的时候接收响应的消息
     *
     * @param processInstanceId 流程实例id
     * @throws InterruptedException 线程异常
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = MAINTAIN_PROCESS_FINISH_QUEUE, autoDelete = "false"),
            exchange = @Exchange(value = "h-visions.process.instance.finish.exchange", type = ExchangeTypes.TOPIC),
            key = ROUTING_KEY),
            errorHandler = "inspectFixErrorHandler"
    )
    public void process(String processInstanceId) throws InterruptedException {
        //增加一段时间的延迟，防止工作流服务不能正确获取结束时间的问题，估计是工作流发出了流程结束时间后，事务并没有结束。导致此时直接访问数据会造成数据异常
        Thread.sleep(1000 * 10);
        service.save(processInstanceId);
    }

    @Bean
    public RabbitListenerErrorHandler inspectFixErrorHandler() {
        return (message, message1, e) -> {
            service.handleError(message, e);
            return null;
        };
    }

}
