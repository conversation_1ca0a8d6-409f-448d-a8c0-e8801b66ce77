package com.hvisions.eam.controller;


import com.hvisions.eam.dto.fault.FaultDTO;
import com.hvisions.eam.dto.fault.FaultQueryDTO;
import com.hvisions.eam.service.fault.FaultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * <p>Title: HvEmFaultController</p >
 * <p>Description: 设备故障 controller</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/26</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@Api(description = "设备故障controller")
@RestController
@Slf4j
@RequestMapping(value = "/EmFault")
public class FaultController {
    /**
     * 故障
     */
    private final FaultService faultService;

    @Autowired
    public FaultController(FaultService faultService) {
        this.faultService = faultService;
    }

    /**
     * 添加故障
     *
     * @param faultDTO 设备故障DTO
     * @return 新增故障id
     */
    @ApiOperation(value = "新增故障")
    @RequestMapping(value = "/addEmFault", method = RequestMethod.POST)
    public int addFault(@RequestBody FaultDTO faultDTO) {
        return faultService.addOrUpdateFault(faultDTO);
    }

    /**
     * 删除故障
     *
     * @param id 故障id
     */
    @ApiOperation(value = "删除故障")
    @RequestMapping(value = "/deleteFault/{id}", method = RequestMethod.DELETE)
    public void deleteFault(@PathVariable Integer id) {
        faultService.deleteFault(id);
    }

    /**
     * 通过code删除故障
     *
     * @param faultCode 故障编码
     */
    @ApiOperation("通过code删除故障")
    @DeleteMapping(value = "/deleteFaultCode/{faultCode}")
    public void deleteFaultCode(@PathVariable String faultCode) {
        faultService.deleteFaultByCode(faultCode);
    }

    /**
     * 通过编码查询故障
     *
     * @param faultCode 故障编码
     * @return 故障
     */
    @ApiOperation(value = "通过code编码查询故障")
    @GetMapping(value = "/getFaultByCode/{faultCode}")
    public FaultDTO getFaultByCode(@PathVariable String faultCode) {
        return faultService.getFaultByCode(faultCode);
    }


    /**
     * 更新故障
     *
     * @param emFaultDTO 故障DTO
     */
    @ApiOperation(value = "更新故障")
    @RequestMapping(value = "updateFault", method = RequestMethod.PUT)
    public void updateFault(@RequestBody FaultDTO emFaultDTO) {
        faultService.addOrUpdateFault(emFaultDTO);
    }


    /**
     * 查询一类故障 表现
     *
     * @param faultQueryDTO 故障类查询故障DTO
     * @return 一类故障集合
     */
    @ApiOperation("查询一类故障")
    @PostMapping(value = "getFaultByFaultClass")
    public Page<FaultDTO> getFaultByFaultClass(@RequestBody FaultQueryDTO faultQueryDTO) {
        return faultService.getFaultByFaultClass(faultQueryDTO);
    }


    /**
     * 根据id查询故障
     *
     * @param id 故障id
     * @return 故障
     */
    @ApiOperation(value = "根据id查询故障")
    @GetMapping(value = "getFaultById/{id}")
    public FaultDTO getFaultById(@PathVariable Integer id) {
        return faultService.getFaultById(id);
    }


    /**
     * 设备数据统计
     *
     * @return 统计数量
     */
    @ApiOperation(value = "设备数据统计")
    @GetMapping(value = "/getFaultAndMaintainEquipmentNum")
    public Map<String, Integer> getFaultAndMaintainEquipmentNum() {
        return faultService.getFaultAndMaintainEquipmentNum();
    }
}