package com.hvisions.eam.service.publicstore;

import com.hvisions.eam.dto.publicstore.UseListingLubDTO;
import com.hvisions.eam.dto.publicstore.UseListingSpareDTO;
import com.hvisions.eam.query.publicstore.UseListingQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title:UseListingService</p>
 * <p>Description:备件使用清单</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface UseListingService {

    /**
     * 增加  使用清单DTO
     *
     * @param useListingSpareDTO 使用清单DTO
     * @return id
     */
    Integer create(UseListingSpareDTO useListingSpareDTO);

    /**
     * 批量增加 使用清单DTO
     *
     * @param useListingSpareDTOS 使用清单DTO
     * @return id
     */
    List<Integer> createList(List<UseListingSpareDTO> useListingSpareDTOS);

    /**
     * 通过 processInstanceId 获取全部关联项
     *
     * @param useListingQueryDTO 关联id
     * @return 分页
     */
    Page<UseListingSpareDTO> getSpareByProcessInstanceId(UseListingQueryDTO useListingQueryDTO);

    /**
     * 通过 processInstanceId 获取全部关联项
     *
     * @param useListingDTO 关联id
     * @return 分页
     */
    Page<UseListingLubDTO> getLubByProcessInstanceId(UseListingQueryDTO useListingDTO);
}
