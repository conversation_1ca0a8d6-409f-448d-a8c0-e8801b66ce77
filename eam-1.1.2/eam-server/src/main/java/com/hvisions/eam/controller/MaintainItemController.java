package com.hvisions.eam.controller;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.eam.dto.maintain.MaintainItemDTO;
import com.hvisions.eam.dto.maintain.MaintainItemQueryDTO;
import com.hvisions.eam.service.maintain.MaintainItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>Title: HvEamMaintainItemController</p >
 * <p>Description: 保养项目接口</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/maintainItem")
@Api(description = "保养项目接口")
public class MaintainItemController {
    private final MaintainItemService maintainItemService;

    @Autowired
    public MaintainItemController(MaintainItemService maintainItemService) {
        this.maintainItemService = maintainItemService;
    }


    /**
     * 新增保养项目
     *
     * @param dto 保养项目DTO
     * @return 新增保养项目id
     */
    @ApiOperation(value = "新增保养项目信息")
    @PostMapping(value = "/createMaintainItem")
    public Integer createMaintainItem(@RequestBody MaintainItemDTO dto) {
        return maintainItemService.save(dto);
    }

    /**
     * 编辑保养项目
     *
     * @param dto 保养项目DTO
     * @return 编辑保养项目id
     */
    @ApiOperation(value = "更新保养项目信息")
    @PutMapping(value = "/updateMaintainItem")
    public Integer updateMaintainItem(@RequestBody MaintainItemDTO dto) {
        return maintainItemService.save(dto);
    }

    /**
     * 删除单个保养项目
     *
     * @param id 要删除的保养项目的id
     */
    @ApiOperation(value = "删除保养项目信息")
    @DeleteMapping(value = "/deleteMaintainItemInfoById/{id}")
    public void deleteMaintainItemInfoById(@PathVariable Integer id) {
        maintainItemService.deleteMaintainItemById(id);
    }


    /**
     * 根据id获取保养项目详情
     *
     * @param id 保养项目id
     * @return 保养项目信息
     */
    @ApiOperation(value = "根据id获取保养项目信息")
    @GetMapping(value = "/getById/{id}")
    public MaintainItemDTO getById(@PathVariable Integer id) {
        return maintainItemService.getById(id);
    }

    /**
     * 根据编码获取保养项目详情
     *
     * @param itemCode 保养项目编码
     * @return 保养项目信息
     */
    @ApiOperation(value = "根据编码获取保养项目信息")
    @GetMapping(value = "/getByCode")
    public MaintainItemDTO getByCode(@RequestParam String itemCode) {
        return maintainItemService.getByCode(itemCode);
    }

    /**
     * 批量删除保养项目
     *
     * @param idList id集合
     */
    @ApiOperation(value = "批量删除保养项目")
    @DeleteMapping(value = "/deleteMaintainItemInfoByIdList")
    public void deleteMaintainItemInfoByIdList(@RequestBody List<Integer> idList) {
        maintainItemService.deleteMaintainItemByIdList(idList);
    }

    /**
     * 根据设备型号或保养部位查询保养项目信息，以及关联的备件，油品信息（模糊分页）
     *
     * @param query 分页查询条件
     * @return 保养项目DTO
     */
    @ApiOperation(value = "根据设备型号和保养部位，是否启用查询保养项目信息，以及关联的备件，油品信息（模糊分页）,已经过期，使用/getMaintainByQuery 接口替代")
    @PostMapping(value = "/getMaintainByQuery")
    public Page<MaintainItemDTO> getMaintainByQuery(@RequestBody MaintainItemQueryDTO query) {
        return maintainItemService.getMaintainByQuery(query);
    }


    /**
     * 是否启用保养项目
     *
     * @param id 要启用或停止的保养项目id
     * @return 返回被操作的id
     */
    @ApiOperation(value = "是否启用保养项目")
    @PutMapping(value = "/startUsingItemById/{id}")
    public Boolean startUsingItemById(@PathVariable Integer id) {
        return maintainItemService.toggle(id);
    }

    @RequestMapping(value = "/exportNew", method = RequestMethod.POST)
    @ApiOperation(value = "导出保养项目")
    public ExcelExportDto exportDataNew(@RequestBody List<Integer> ids) {
        return maintainItemService.export(ids);
    }

    @RequestMapping(value = "/importNew", method = RequestMethod.POST)
    @ApiOperation(value = "导入保养项目")
    public ImportResult importDataNew(@RequestParam("file") MultipartFile file) {
        return maintainItemService.importData(file);
    }

    @RequestMapping(value = "/excel/templateNew", method = RequestMethod.POST)
    @ApiOperation(value = "excel模板导出")
    public ExcelExportDto exportTemplateNew() {
        return maintainItemService.exportTemplate();
    }

    /**
     * 获取所有周期
     *
     * @return 周期集合
     */
    @RequestMapping(value = "/getAllCycles", method = RequestMethod.GET)
    @ApiOperation(value = "获取所有周期")
    public List<String> getAllCycles() {
        return maintainItemService.getAllCycles();
    }


}
