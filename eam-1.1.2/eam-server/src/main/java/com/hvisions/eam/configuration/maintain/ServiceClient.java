package com.hvisions.eam.configuration.maintain;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.EquipmentFeignClient;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.location.LocationMsgDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: ServiceClient</p>
 * <p>Description: 服务调用接口</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/12/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class ServiceClient {
    @Autowired
    EquipmentFeignClient client;

    public Optional<EquipmentDTO> get(Integer id) {
        ResultVO<EquipmentDTO> result = client.getEquipmentById(id);
        if (!result.isSuccess()) {
            return null;
        }
        return Optional.ofNullable(result.getData());
    }

    public List<EquipmentDTO> getList(List<Integer> id) {
        ResultVO<List<EquipmentDTO>> result = client.getEquipmentById(id);
        if (!result.isSuccess()) {
            return null;
        }
        return Optional.ofNullable(result.getData()).orElse(new ArrayList<>());
    }

    public Optional<LocationMsgDTO> getLocationInfo(Integer id) {
        ResultVO<LocationMsgDTO> result = client.getLocationMsgDtoByEquipmentId(id);
        if (!result.isSuccess()) {
            return null;
        } else {
            return Optional.ofNullable(result.getData());
        }
    }
}









