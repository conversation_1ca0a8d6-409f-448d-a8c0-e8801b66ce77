package com.hvisions.eam.service.publicstore;

import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.eam.activiti.lub.RootLubDTO;
import com.hvisions.eam.activiti.sapre.RootSpareDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;

/**
 * <p>Title: ItemDTO</p>
 * <p>Description: 申请备件表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface ApplyService {

    //----------------------------------= 入库申请 =---------------------------------------

    /**
     * 申请备件
     *
     * @param rootSpareDTO 申请单
     * @return 申请单
     */
    RootSpareDTO applySpare(RootSpareDTO rootSpareDTO);

    /**
     * 导入备件入库申请信息信息
     *
     * @param file 备件入库申请信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importInSpare(MultipartFile file) throws IllegalAccessException, ParseException, IOException;


    /**
     * 导入备件出库库申请信息信息
     *
     * @param file 备件出库申请信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importOutSpare(MultipartFile file) throws IllegalAccessException, ParseException, IOException;

    /**
     * 申请油品
     *
     * @param rootLubDTO 申请单
     * @return 申请单
     */
    RootLubDTO applyLub(RootLubDTO rootLubDTO);

    //----------------------------------= 审批 =------------------------------------------

    /**
     * 备件 油品出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3
     *
     * @param taskId    流程ID
     * @param isPass    审批状态
     * @param typeClass 类型
     * @param token    token
     */
    void spareAndLubIsThrough(String taskId, Integer isPass, int typeClass,String token);

    //------------------------------------= 出库 =--------------------------------------------------

    /**
     * 出库
     *
     * @param processInstanceId 流程ID
     * @param isPass            是否通过
     * @param type              类型 1 备件 2 油品
     * @return 出库表ID
     */
    Integer spareAndLubOut(String processInstanceId, Boolean isPass, Integer type);

    //------------------------------------= 过期 =--------------------------------------------------

    /**
     * 生成唯一不重复流水单号
     *
     * @return 唯一不重复流水单号
     */
    String getGodownReceiptNumber();

    /**
     * 生成唯一不重复的单号 生成规则  年 月 日 流水号（四位）共计12位
     *
     * @param service 服务名
     * @return 唯一单号
     */
    String getSerialNumber(String service);


}