package com.hvisions.eam.repository.publicstore;

import com.hvisions.eam.entity.publicstore.HvEamActualUse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title:AtualUseRepository</p>
 * <p>Description:备件实际使用</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface ActualUseRepository extends JpaRepository<HvEamActualUse, Integer> {

    /**
     * 通过关系查询出当前关系下的全部实际使用
     *
     * @param processInstanceId 关联项ID
     * @param pageable          分页信息
     * @return 分页
     */
    Page<HvEamActualUse> findByProcessInstanceId(String processInstanceId, Pageable pageable);

    /**
     * 通过processInstanceIds 获取list集合
     *
     * @param processInstanceId 流程实例Id
     * @return list
     */
    List<HvEamActualUse> findByProcessInstanceId(String processInstanceId);

    /**
     * 通过 备件 库房 批次号 获取实际申请
     *
     * @param spareId           备件id
     * @param shelveId          库房id
     * @param batchNumber       批次号
     * @param identifierKey     关联项
     * @param processInstanceId 流程实例
     * @return 实际申请
     */
    HvEamActualUse findBySpareIdAndShelveIdAndBatchNumberAndIdentifierKeyAndProcessInstanceId(Integer spareId, Integer shelveId, String batchNumber, String identifierKey, String processInstanceId);

    /**
     * 通过备件id 库房id 批次号 流程id
     *
     * @param spareId           备件id
     * @param shelveId          库房id
     * @param batchNumber       批次号
     * @param processInstanceId 流程id
     * @return 数据
     */
    List<HvEamActualUse> findBySpareIdAndShelveIdAndBatchNumberAndProcessInstanceId(Integer spareId, Integer shelveId, String batchNumber, String processInstanceId);
}
