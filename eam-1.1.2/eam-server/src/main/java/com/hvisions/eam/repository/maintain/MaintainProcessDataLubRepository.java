package com.hvisions.eam.repository.maintain;

import com.hvisions.eam.entity.maintain.HvEamMaintainProcessDataLub;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: MaintainProcessDataRepository</p>
 * <p>Description: 保养数据保存仓储对象</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/10/14</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaintainProcessDataLubRepository extends JpaRepository<HvEamMaintainProcessDataLub, Integer> {
}









