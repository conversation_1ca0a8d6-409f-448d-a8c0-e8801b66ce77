package com.hvisions.eam.service.publicstore.imp;

import com.hvisions.activiti.client.ActivitiClient;
import com.hvisions.activiti.dto.history.HistoricProcessQuery;
import com.hvisions.activiti.dto.history.HistoryProcessInstanceDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.lub.ItemsLubDTO;
import com.hvisions.eam.activiti.sapre.ItemsSpareDTO;
import com.hvisions.eam.configuration.spare.StoreConfig;
import com.hvisions.eam.dto.publicstore.*;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.entity.lub.HvEamLubType;
import com.hvisions.eam.entity.lub.HvEamLubricating;
import com.hvisions.eam.repository.lub.LubEntityRepository;
import com.hvisions.eam.repository.lub.LubTypeEntityRepository;
import com.hvisions.eam.query.publicstore.ActualUseQueryDTO;
import com.hvisions.eam.query.publicstore.HeaderQueryDTO;
import com.hvisions.eam.dao.StoreDao;
import com.hvisions.eam.entity.publicstore.HvEamActualUse;
import com.hvisions.eam.entity.publicstore.HvEamShelve;
import com.hvisions.eam.entity.publicstore.HvEamSpareUnit;
import com.hvisions.eam.repository.publicstore.ActualUseRepository;
import com.hvisions.eam.repository.publicstore.ShelveEntityRepository;
import com.hvisions.eam.repository.publicstore.SpareUnitEntityRepository;
import com.hvisions.eam.service.publicstore.ActualUseService;
import com.hvisions.eam.service.publicstore.StoreHeaderService;
import com.hvisions.eam.dto.spare.SpareItemDTO;
import com.hvisions.eam.entity.spare.HvEamSpare;
import com.hvisions.eam.entity.spare.HvEamSpareType;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import com.hvisions.eam.repository.sprare.SpareTypeEntityRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title:ActualUseServiceImpl</p>
 * <p>Description:实际使用备件列表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Service
public class ActualUseServiceImpl implements ActualUseService {

    /**
     * 是否开启审批
     */
    @Value("${h-visions.approve}")
    Boolean isApproval;

    /**
     * 备件实际使用jpa
     */
    private final ActualUseRepository actualUseRepository;

    /**
     * 库房jpa
     */
    private final ShelveEntityRepository shelveEntityRepository;

    /**
     * 备件jpa
     */
    private final SpareEntityRepository spareEntityRepository;

    /**
     * 油品jpa
     */
    private final LubEntityRepository lubEntityRepository;

    /**
     * 流程id
     */
    private final ActivitiClient activitiClient;
    private final SpareTypeEntityRepository spareTypeEntityRepository;

    /**
     * 出库单
     */
    private final StoreHeaderService storeHeaderService;

    private final StoreDao StoreDao;

    private final LubTypeEntityRepository lubTypeEntityRepository;
    /**
     * 单位jpa
     */
    private final SpareUnitEntityRepository unitEntityRepository;

    private static final Integer SPARE_PART_TYPE = 1;

    private static final Integer LUB_TYPE = 2;

    @Autowired
    public ActualUseServiceImpl(ActualUseRepository actualUseRepository,
                                SpareEntityRepository spareEntityRepository,
                                ShelveEntityRepository shelveEntityRepository,
                                LubEntityRepository lubEntityRepository,
                                ActivitiClient activitiClient,
                                SpareTypeEntityRepository spareTypeEntityRepository, StoreHeaderService storeHeaderService,
                                StoreDao StoreDao,
                                LubTypeEntityRepository lubTypeEntityRepository, SpareUnitEntityRepository unitEntityRepository) {
        this.actualUseRepository = actualUseRepository;
        this.spareEntityRepository = spareEntityRepository;
        this.activitiClient = activitiClient;
        this.shelveEntityRepository = shelveEntityRepository;
        this.lubEntityRepository = lubEntityRepository;
        this.spareTypeEntityRepository = spareTypeEntityRepository;
        this.storeHeaderService = storeHeaderService;
        this.StoreDao = StoreDao;
        this.lubTypeEntityRepository = lubTypeEntityRepository;
        this.unitEntityRepository = unitEntityRepository;
    }

    /**
     * 增加
     *
     * @param actualUseDTO 使用实际使用DTO
     * @return id
     */
    @Override
    public synchronized Integer create(ActualUseDTO actualUseDTO) {
        //数据校验
        if (actualUseDTO.getNumber() == null) {
            throw new BaseKnownException(StoreExceptionEnum.NUMBER_EXCEPTION);
        }
        if (actualUseDTO.getType() == null) {
            throw new BaseKnownException(StoreExceptionEnum.TYPE_EXCEPTION);
        }
        if (Strings.isBlank(actualUseDTO.getBatchNumber())) {
            throw new BaseKnownException(StoreExceptionEnum.BATCH_NUMBER_INFORMATION_EXCEPTION);
        }
        if (Strings.isBlank(actualUseDTO.getProcessInstanceId())) {
            throw new BaseKnownException(StoreExceptionEnum.DATE_ERROR_EXCEPTION);
        }
        if (actualUseDTO.getNumber().compareTo(BigDecimal.ZERO) < 0) {
            throw new BaseKnownException(StoreExceptionEnum.NUMBER_EXCEPTION);
        }
        //通过备件ID 库房ID 关联项 流程ID 获取当前备件实际使用数据
        HvEamActualUse actualUse = actualUseRepository
                .findBySpareIdAndShelveIdAndBatchNumberAndIdentifierKeyAndProcessInstanceId(
                        actualUseDTO.getSpareId(),
                        actualUseDTO.getShelveId(),
                        actualUseDTO.getBatchNumber(),
                        actualUseDTO.getIdentifierKey(),
                        actualUseDTO.getProcessInstanceId());
        //直接跳过数量为0的数据
        if (actualUseDTO.getNumber().compareTo(BigDecimal.ZERO) == 0) {
            return 0;
        }
        //如果获取到数据
        if (actualUse != null) {
            //修改原数据
            actualUse.setNumber(actualUseDTO.getNumber().add(actualUse.getNumber()));
            return actualUseRepository.save(actualUse).getId();
        }
        //如果数据库中无数据 且 数量不为零那么就添加数据
        return actualUseRepository
                .save(DtoMapper.convert(actualUseDTO, HvEamActualUse.class, "id"))
                .getId();
    }

    /**
     * 批量增加
     *
     * @param actualUseDTOS 使用清单DTO List
     * @return id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Integer> createActualUseList(List<ActualUseDTO> actualUseDTOS) {
        List<Integer> ids = new ArrayList<>();
        for (ActualUseDTO actualUseDTO : actualUseDTOS) {
            //当数量大于0的时候不进行增加操作
            if (actualUseDTO.getNumber().compareTo(BigDecimal.ZERO) > 0) {
                Integer id = create(actualUseDTO);
                ids.add(id);
            }
        }
        return ids;
    }

    /**
     * 修改
     *
     * @param actualUseDTO 使用清单DTO
     * @return id
     */
    @Override
    public Integer update(ActualUseDTO actualUseDTO) {
        // 查出总使用数量

        // 查出已使用数量
        return actualUseRepository.save(DtoMapper.convert(actualUseDTO, HvEamActualUse.class)).getId();

    }

    /**
     * 通过id删除
     *
     * @param id id
     */
    @Override
    public void deleteById(Integer id) {
        actualUseRepository.deleteById(id);
    }

    /**
     * 通过Id list删除
     *
     * @param ids id
     */
    @Override
    public void deleteByListId(List<Integer> ids) {
        for (Integer id : ids) {
            actualUseRepository.deleteById(id);
        }
    }

    /**
     * 获取实际使用列表
     *
     * @param actualUseQueryDTO 关联id
     * @return 分页
     */
    @Override
    public Page<ActualUseDTO> getActual(ActualUseQueryDTO actualUseQueryDTO) {

        HvEamActualUse eamActualUse = DtoMapper.convert(actualUseQueryDTO, HvEamActualUse.class);

        //匹配方式       xxx 采用开始匹配的方式查询 忽略大小写
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("processInstanceId", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("identifierKey", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("type", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());

        Example<HvEamActualUse> example = Example.of(eamActualUse, exampleMatcher);
        Page<HvEamActualUse> page = actualUseRepository.findAll(example, actualUseQueryDTO.getRequest());
        Page<ActualUseDTO> actualUseDTOS = DtoMapper.convertPage(page, ActualUseDTO.class);
        actualUseDTOS.stream().peek(t -> {
            if (t.getType() == 1) {
                Optional<HvEamSpare> spareOpt = spareEntityRepository.findById(t.getSpareId());
                if (spareOpt.isPresent()) {
                    t.setSpareName(spareOpt.get().getSpareName());
                } else {
                    t.setSpareName("---");
                }
            } else {
                Optional<HvEamLubricating> lubOpt = lubEntityRepository.findById(t.getSpareId());
                if (lubOpt.isPresent()) {
                    t.setSpareName(lubOpt.get().getLubName());
                } else {
                    t.setSpareName("---");
                }
            }
            Optional<HvEamShelve> shelveOpt = shelveEntityRepository.findById(t.getShelveId());
            if (shelveOpt.isPresent()) {
                t.setShelveName(shelveOpt.get().getShelveName());
            } else {
                t.setShelveName("---");
            }
        }).collect(Collectors.toList());
        return actualUseDTOS;
    }


    /**
     * 通过actualUsePageDTO 获取 申请列表(内部调用)
     *
     * @param processInstanceId    关联id
     * @param processDefinitionKey 流程定义
     * @return 申请列表
     */
    private List<HistoryProcessInstanceDTO> getHistoricTaskInstanceDTOList(String processInstanceId, String processDefinitionKey) {
        //通过关系的id获取备件实际申请
        HistoricProcessQuery historyProcessInstanceDTO = new HistoricProcessQuery();
        //  "备件-采购"
        historyProcessInstanceDTO.setBusinessKey(processInstanceId);
        // "store-spare"
        historyProcessInstanceDTO.setProcessDefinitionKey(processDefinitionKey);
        ResultVO<List<HistoryProcessInstanceDTO>> historicTaskInstanceByKey = activitiClient.getHistoricProcessInstanceList(historyProcessInstanceDTO);
        if (!historicTaskInstanceByKey.isSuccess()) {
            throw new BaseKnownException(StoreExceptionEnum.WORK_FLOW_SERVER_ERROR);
        }
        return historicTaskInstanceByKey.getData();
    }

    // TODO: 2019/8/26 诺到头表中去
    private List<LineDTO> merge(Page<HeaderDTO> header) {
        //定义一个集合项
        List<LineDTO> lineDTO = new ArrayList<>();
        for (HeaderDTO headerDTO : header) {
            lineDTO.addAll(headerDTO.getLineDTOS());
        }
        //定义一个返回项
        List<LineDTO> lineDTOS = new ArrayList<>();
        //遍历 如果相同则数量相加
        for (int i = 0; i < lineDTO.size(); i++) {
            boolean file = true;
            for (int j = 0; j < lineDTOS.size(); j++) {
                if (lineDTO.get(i).equals(lineDTOS.get(j))) {
                    lineDTOS.get(j).setNumber(lineDTOS.get(j).getNumber().add(lineDTO.get(i).getNumber()));
                    file = false;
                    break;
                }
            }
            if (file) {
                lineDTOS.add(lineDTO.get(i));
            }
        }
        return lineDTOS;
    }

    /**
     * 获取备件实际申请列表 以集合形式返回
     *
     * @param actualUseQueryDTO 查询条件
     * @return 全部信息
     */
    private List<ActualUseDTO> getApplyThrough(ActualUseQueryDTO actualUseQueryDTO, Integer typeClass) {
        List<ActualUseDTO> actualUseDTOS = new ArrayList<>();
        String processInstanceId = actualUseQueryDTO.getProcessInstanceId();
        HeaderQueryDTO headerQueryDTO = new HeaderQueryDTO();
        headerQueryDTO.setProcessInstanceId(processInstanceId);
        headerQueryDTO.setComplete(true);
        headerQueryDTO.setTypeClass(typeClass);
        Page<HeaderDTO> header = storeHeaderService.getHeader(headerQueryDTO);
        //如果出现多条数据 相同的数量合并
        List<LineDTO> merge = merge(header);
        for (LineDTO lineDTO : merge) {
            ActualUseDTO actualUseDTO = new ActualUseDTO(lineDTO.getSpareId(), lineDTO.getShelveId(), lineDTO.getBatchNumber(), lineDTO.getNumber(), actualUseQueryDTO.getProcessInstanceId(), actualUseQueryDTO.getIdentifierKey());
            actualUseDTO.setSpareName(lineDTO.getSpareName());
            actualUseDTO.setShelveName(lineDTO.getShelveName());
            HvEamActualUse actual = actualUseRepository.findBySpareIdAndShelveIdAndBatchNumberAndIdentifierKeyAndProcessInstanceId(lineDTO.getSpareId(), lineDTO.getShelveId(), lineDTO.getBatchNumber(), actualUseQueryDTO.getIdentifierKey(), actualUseQueryDTO.getProcessInstanceId());
            if (actual == null) {
                actualUseDTO.setNumber(new BigDecimal(0));
            } else {
                actualUseDTO.setNumber(actual.getNumber());
            }
            try {
                actualUseDTO.setUnitDTO(this.getUnitBySpareId(actualUseDTO.getSpareId(), typeClass));
            } catch (Exception e) {
                log.info("单位有异常");
                actualUseDTO.setUnitDTO(new UnitDTO());
            }

            actualUseDTOS.add(actualUseDTO);
        }
        return actualUseDTOS;
    }

    /**
     * 通过备件id获取 对应的单位信息
     *
     * @param spareId 备件id
     * @return 单位信息
     */
    private UnitDTO getUnitBySpareId(Integer spareId, Integer typeClass) {
        Integer unitId = 0;
        if (StoreConfig.SPARE_TYPECLASS.equals(typeClass)) {
            Optional<HvEamSpare> optional = spareEntityRepository.findById(spareId);
            if (optional.isPresent()) {
                unitId = optional.get().getUnitId();
            }
        } else if (StoreConfig.LUB_TYPECLASS.equals(typeClass)) {
            Optional<HvEamLubricating> optional = lubEntityRepository.findById(spareId);
            if (optional.isPresent()) {
                unitId = optional.get().getUnitId();
            }
        }
        Optional<HvEamSpareUnit> optiona = unitEntityRepository.findById(unitId);
        return optiona.map(hvEamSpareUnit -> DtoMapper.convert(hvEamSpareUnit, UnitDTO.class)).orElse(null);
    }

    /**
     * 获取备件实际申请列表 以集合形式返回
     *
     * @param actualUseQueryDTO 查询条件
     * @return 全部信息
     */
    @Override
    @SuppressWarnings(StoreConfig.RULES)
    public List<ActualUseDTO> spareApplyThrough(@RequestBody ActualUseQueryDTO actualUseQueryDTO, String processDefinitionKey) {
        return getApplyThrough(actualUseQueryDTO, StoreConfig.SPARE_TYPECLASS);
    }

    /**
     * 获取备件实际申请列表 以集合形式返回
     *
     * @param actualUseQueryDTO 查询条件
     * @return 全部信息
     */
    @Override
    public List<ActualUseDTO> lubApplyThrough(@RequestBody ActualUseQueryDTO actualUseQueryDTO, String processDefinitionKey) {
        return getApplyThrough(actualUseQueryDTO, StoreConfig.LUB_TYPECLASS);
    }

    /**
     * 获取备件或者油品的申请信息
     *
     * @param processInstanceBusinessKey 唯一标识符
     * @param type                       类型 1备件 2 油品
     * @return 申请信息
     */
    @Override
    public List<ApplyDTO> getSpareOrLubApply(String processInstanceBusinessKey, Integer type) {
        //全部申请项
        List<ApplyDTO> applyDTOS = new ArrayList<>();
        //开启审批
        if (isApproval) {
            final String processDefinitionKey;
            if (type == 1) {
                processDefinitionKey = "store-spare";
            } else {
                processDefinitionKey = "store-lub";
            }
            //定义实际申请列表 获取数据
            List<HistoryProcessInstanceDTO> historicTaskInstanceDTOList = getHistoricTaskInstanceDTOList(processInstanceBusinessKey, processDefinitionKey);
            //每个申请订单
            for (HistoryProcessInstanceDTO historicTaskInstanceDTO : historicTaskInstanceDTOList) {
                //每一个申请项
                ApplyDTO applyDTO = new ApplyDTO();
                //获取申请项
                Map<String, Object> historyVariables = historicTaskInstanceDTO.getProcessInstanceVariables();
                applyDTO.setApplyUserName(historyVariables.get("applyUserName") + "");
                applyDTO.setTitle(historyVariables.get("title") + "");
                applyDTO.setAccessType(historyVariables.get("accessTy") + "");
                applyDTO.setReceiptNumber(historyVariables.get("receiptNumber") + "");
                applyDTO.setRemarks(historyVariables.get("remarks") + "");
                applyDTO.setInOut(Integer.parseInt(historyVariables.get("inOut") + ""));
                int isPass = Integer.parseInt(historyVariables.get("isPass") + "");
                if (isPass == 1) {
                    applyDTO.setExaminationType("审核中");
                } else if (isPass == 2) {
                    applyDTO.setExaminationType("审核通过");
                } else if (isPass == 3) {
                    applyDTO.setExaminationType("审核驳回");
                }
                boolean a = isComplete(historyVariables.get("receiptNumber") + "", type);
                if (a) {
                    applyDTO.setExaminationType("已完成");
                }
                List<Map<String, Object>> itemsa = (List<Map<String, Object>>) historyVariables.get("items");
                //每一个申请单对应的申请项
                List<SpareItemDTO> items = new ArrayList<>();
                if (itemsa != null && itemsa.size() > 0) {
                    for (int i = 0; i < itemsa.size(); i++) {
                        SpareItemDTO spareItemDTO = new SpareItemDTO();
                        if (type == 1) {
                            try {
                                Integer spareId = Integer.parseInt(itemsa.get(i).get("spareId").toString());
                                spareEntityRepository.findById(spareId)
                                        .ifPresent(t -> {
                                            spareItemDTO.setSpareName(t.getSpareName());
                                            spareItemDTO.setBrand(t.getBrand());
                                            spareItemDTO.setSupplier(t.getSupplier());
                                            if (t.getSpareTypeId() != null) {
                                                spareItemDTO.setTypeName(spareTypeEntityRepository.findById(t.getSpareTypeId()).map(HvEamSpareType::getTypeName).orElse(""));
                                            }
                                        });
                            } catch (Exception e) {
                                log.warn("获取备件信息异常{}", e.getMessage());
                            }
                        } else {
                            try {
                                Integer lubId = Integer.parseInt(itemsa.get(i).get("lubId").toString());
                                Optional.of(lubEntityRepository.findById(lubId)).ifPresent(
                                        l -> {
                                            spareItemDTO.setSpareName(l.get().getLubName());
                                            spareItemDTO.setTypeName(lubTypeEntityRepository.findById(l.get().getLubTypeId()).map(HvEamLubType::getTypeName).orElse(""));
                                        }
                                );
                            } catch (Exception e) {
                                log.warn("获取油品信息异常:{}", e.getMessage());
                                spareItemDTO.setSpareName("");
                            }
                        }
                        if (!"null".equals((itemsa.get(i).get("shelveId") + ""))) {
                            //设置库房id
                            spareItemDTO.setShelveId(Integer.parseInt(itemsa.get(i).get("shelveId").toString()));
                            //查询库房
                            if (shelveEntityRepository.existsById(Integer.parseInt(itemsa.get(i).get("shelveId") + ""))) {
                                HvEamShelve shelveId = shelveEntityRepository.getOne(Integer.parseInt(itemsa.get(i).get("shelveId") + ""));
                                //设置库房名称
                                spareItemDTO.setShelveName(shelveId.getShelveName());
                            } else {
                                //设置库房名称
                                spareItemDTO.setShelveName("");
                            }
                        } else {
                            spareItemDTO.setShelveId(0);
                            spareItemDTO.setShelveName("");
                        }
                        if (!"null".equals((itemsa.get(i).get("batchNumber") + ""))) {
                            //设置批次号
                            spareItemDTO.setBatchNumber(itemsa.get(i).get("batchNumber") + "");
                        } else {
                            spareItemDTO.setBatchNumber("");
                        }
                        try {
                            //设置数量
                            spareItemDTO.setNumber(new BigDecimal(itemsa.get(i).get("number") + ""));
                        } catch (Exception e) {
                            log.info("数量转换异常");
                            spareItemDTO.setNumber(BigDecimal.ZERO);
                        }
                        //添加每个申请项
                        items.add(spareItemDTO);
                    }
                }
                //每个申请单添加申请项
                applyDTO.setItems(items);
                //添加每一个申请单
                applyDTOS.add(applyDTO);
            }
        } else {
            //关闭审批
            HeaderQueryDTO headerQueryDTO = new HeaderQueryDTO();
            headerQueryDTO.setProcessInstanceId(processInstanceBusinessKey);
            headerQueryDTO.setTypeClass(type);
            //获取当前的所有出库单
            List<HeaderDTO> headerList = storeHeaderService.getHeaderList(headerQueryDTO);
            for (HeaderDTO headerDTO : headerList) {
                ApplyDTO applyDTO = DtoMapper.convert(headerDTO, ApplyDTO.class);

                //根据是出库单是否完成返回状态
                if (headerDTO.getComplete()) {
                    if (headerDTO.getReject() == null) {
                        applyDTO.setExaminationType("已完成");
                    } else if (headerDTO.getReject().equals(1)) {
                        applyDTO.setExaminationType("审核驳回");
                    } else {
                        log.warn("异常情况。这里的数据不能为其他数据");
                        applyDTO.setExaminationType("已完成");
                    }
                } else {
                    applyDTO.setExaminationType("待执行");
                }
                //每一项
                List<SpareItemDTO> items = new ArrayList<>();
                for (LineDTO lineDTO : headerDTO.getLineDTOS()) {
                    SpareItemDTO item = DtoMapper.convert(lineDTO, SpareItemDTO.class);
                    item.setNumber(lineDTO.getNumber());
                    items.add(item);
                }
                applyDTO.setItems(items);
                applyDTOS.add(applyDTO);
            }
        }
        return applyDTOS;
    }

    private boolean isComplete(String receiptNumber, int typeClass) {
        log.info(receiptNumber);
        HeaderQueryDTO headerQueryDTO = new HeaderQueryDTO();
        headerQueryDTO.setReceiptNumber(receiptNumber);
        headerQueryDTO.setTypeClass(typeClass);
        Page<HeaderDTO> header = storeHeaderService.getHeader(headerQueryDTO);
        if (header == null) {
            return false;
        }
        for (HeaderDTO headerDTO : header) {
            log.info(headerDTO.toString());
        }
        List<HeaderDTO> content = header.getContent();
        if (content.size() <= 0) {
            return false;
        }
        HeaderDTO headerDTO = content.get(0);
        return headerDTO.getComplete();
    }

    /**
     * 通过BusinessKey获取全部申请通过的申请项数据
     *
     * @param processInstanceBusinessKey 唯一标识符
     * @return 申请项 list
     */
    @SuppressWarnings(StoreConfig.RULES)
    @Override
    public List<ItemsSpareDTO> getSpareApplyList(String processInstanceBusinessKey) {
        List<ItemsSpareDTO> spareItemList = new ArrayList<>();
        if (isApproval) {
            //定义实际申请列表 获取数据
            List<HistoryProcessInstanceDTO> historicTaskInstanceDTOList = getHistoricTaskInstanceDTOList(processInstanceBusinessKey, "store-spare");
            //每个申请订单
            for (HistoryProcessInstanceDTO historicTaskInstanceDTO : historicTaskInstanceDTOList) {
                //获取申请项
                Map<String, Object> historyVariables = historicTaskInstanceDTO.getProcessInstanceVariables();
                //如果不是审批状态为通过则放弃本次循环
                if ("2".equals(historyVariables.get("isPass") + "")) {
                    List<Map<String, Object>> itemsa = (List<Map<String, Object>>) historyVariables.get("items");
                    //每一个申请单对应的申请项
                    if (itemsa != null && itemsa.size() > 0) {
                        //遍历每一个申请项
                        for (int i = 0; i < itemsa.size(); i++) {
                            //创建申请项
                            ItemsSpareDTO spareItem = new ItemsSpareDTO();
                            //获取备件id
                            spareItem.setSpareId(Integer.parseInt(itemsa.get(i).get("spareId") + ""));
                            if (spareEntityRepository.existsById(Integer.parseInt(itemsa.get(i).get("spareId") + ""))) {
                                HvEamSpare spareId = spareEntityRepository.getOne(Integer.parseInt(itemsa.get(i).get("spareId") + ""));
                                //设置备件名称
                                spareItem.setSpareName(spareId.getSpareName());
                            } else {
                                spareItem.setSpareName("");
                            }
                            if (!"null".equals((itemsa.get(i).get("shelveId") + ""))) {
                                //设置库房id
                                spareItem.setShelveId(Integer.parseInt(itemsa.get(i).get("shelveId") + ""));
                                //查询库房
                                if (shelveEntityRepository.existsById(Integer.parseInt(itemsa.get(i).get("shelveId") + ""))) {
                                    HvEamShelve shelveId = shelveEntityRepository.getOne(Integer.parseInt(itemsa.get(i).get("shelveId") + ""));
                                    //设置库房名称
                                    spareItem.setShelveName(shelveId.getShelveName());
                                } else {
                                    //设置库房名称
                                    spareItem.setShelveName("");
                                }
                            } else {
                                spareItem.setShelveId(0);
                                spareItem.setShelveName("");
                            }
                            if (!"null".equals((itemsa.get(i).get("batchNumber") + ""))) {
                                //设置批次号
                                spareItem.setBatchNumber(itemsa.get(i).get("batchNumber") + "");
                            } else {
                                spareItem.setBatchNumber("");
                            }
                            //添加备件项
                            spareItemList.add(spareItem);
                        }
                    }
                } else {
                    continue;
                }
            }
        } else {
            List<SpareItemDTO> items = new ArrayList<>();
            List<ApplyDTO> spareOrLubApply = getSpareOrLubApply(processInstanceBusinessKey, 1);
            for (ApplyDTO applyDTO : spareOrLubApply) {
                items.addAll(applyDTO.getItems());
            }
            for (SpareItemDTO item : items) {
                ItemsSpareDTO spareDTO = DtoMapper.convert(item, ItemsSpareDTO.class);

                spareDTO.setNumber(item.getNumber());

                spareItemList.add(spareDTO);
            }
        }
        return spareItemList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<ItemsLubDTO> getLubApplyList(String processInstanceBusinessKey) {
        List<ItemsLubDTO> lubItemList = new ArrayList<>();
        //定义实际申请列表 获取数据
        List<HistoryProcessInstanceDTO> historicTaskInstanceDTOList = getHistoricTaskInstanceDTOList(processInstanceBusinessKey, "store-lub");
        //每个申请订单
        for (HistoryProcessInstanceDTO historicTaskInstanceDTO : historicTaskInstanceDTOList) {
            //获取申请项
            Map<String, Object> historyVariables = historicTaskInstanceDTO.getProcessInstanceVariables();
            //如果不是审批状态为通过则放弃本次循环
            if ("2".equals(historyVariables.get("isPass") + "")) {
                List<Map<String, Object>> itemsa = (List<Map<String, Object>>) historyVariables.get("items");
                //每一个申请单对应的申请项
                if (itemsa != null && itemsa.size() > 0) {
                    //遍历每一个申请项
                    for (int i = 0; i < itemsa.size(); i++) {
                        //创建申请项
                        ItemsLubDTO lubItem = new ItemsLubDTO();
                        //获取备件id
                        lubItem.setLubId(Integer.parseInt(itemsa.get(i).get("lubId") + ""));
                        if (lubEntityRepository.existsById(Integer.parseInt(itemsa.get(i).get("lubId") + ""))) {
                            HvEamLubricating lubId = lubEntityRepository.getOne(Integer.parseInt(itemsa.get(i).get("lubId") + ""));
                            //设置备件名称
                            lubItem.setLubName(lubId.getLubName());
                        } else {
                            lubItem.setLubName("");
                        }
                        if (!"null".equals((itemsa.get(i).get("shelveId") + ""))) {
                            //设置库房id
                            lubItem.setShelveId(Integer.parseInt(itemsa.get(i).get("shelveId") + ""));
                            //查询库房
                            if (shelveEntityRepository.existsById(Integer.parseInt(itemsa.get(i).get("shelveId") + ""))) {
                                HvEamShelve shelveId = shelveEntityRepository.getOne(Integer.parseInt(itemsa.get(i).get("shelveId") + ""));
                                log.info("");
                                //设置库房名称
                                lubItem.setShelveName(shelveId.getShelveName());
                            } else {
                                //设置库房名称
                                lubItem.setShelveName("");
                            }
                        } else {
                            lubItem.setShelveName("");
                            lubItem.setShelveId(-1);

                        }
                        if (!"null".equals((itemsa.get(i).get("batchNumber") + ""))) {
                            //设置批次号
                            lubItem.setBatchNumber(itemsa.get(i).get("batchNumber") + "");
                        } else {
                            lubItem.setBatchNumber("");
                        }
                        //添加备件项
                        lubItemList.add(lubItem);
                    }
                }
            } else {
                continue;
            }
        }
        return lubItemList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 通过流程Id获取备件实际使用List
     *
     * @param processInstanceIds 流程实例Id
     * @return 实际使用List
     */
    @Override
    public List<ActualUseDTO> getActualUseByProcessInstanceIds(List<String> processInstanceIds) {
        //获取备件实际使用
        List<HvEamActualUse> actualUses = new ArrayList<>();
        for (String processInstanceId : processInstanceIds) {
            actualUses.addAll(actualUseRepository.findByProcessInstanceId(processInstanceId));
        }
        //添加名称等属性 并返回
        return DtoMapper.convertList(actualUses, ActualUseDTO.class).stream().peek((t) -> {
            //如果类型为备件
            if (t.getType() == 1) {
                Optional<HvEamSpare> spare = spareEntityRepository.findById(t.getSpareId());
                if (spare.isPresent()) {
                    t.setSpareName(spare.get().getSpareName());
                    t.setUnitPrice(spare.get().getUnitPrice());
                    t.setImg(spare.get().getImg());
                }
                //类型为油品
            } else {
                Optional<HvEamLubricating> lub = lubEntityRepository.findById(t.getSpareId());
                if (lub.isPresent()) {
                    t.setSpareName(lub.get().getLubName());
                    t.setUnitPrice(lub.get().getUnitPrice());
                    t.setImg(lub.get().getImg());
                }
            }
            //添加库房信息
            Optional<HvEamShelve> shelve = shelveEntityRepository.findById(t.getShelveId());
            shelve.ifPresent(hvEamShelve -> t.setShelveName(hvEamShelve.getShelveName()));
        }).collect(Collectors.toList());
    }


    /**
     * 通过businessKey 获取备件申请通过并出库的 备件
     *
     * @param processInstanceId 流程实例id
     * @param type              1：备件，2：油品
     * @return 申请通过并出库 可以使用的备件
     */
    @Override
    public List<SpareItemDTO> getSpareOrLubCanUse(String processInstanceId, Integer type) {
        //只拿已经出库的信息
        StoreQuery query = new StoreQuery();
        query.setProcessInstanceId(processInstanceId);
        //出库
        query.setInOrOut(2);
        //类型,1:备件，2：油品
        query.setType(type);
        //完成
        query.setIsComplete(true);
        List<SpareItemDTO> spareItemDtos = new ArrayList<>();
        if (LUB_TYPE.equals(type)) {
            spareItemDtos = StoreDao.getAllLub(query);
        } else if (SPARE_PART_TYPE.equals(type)) {
            spareItemDtos = StoreDao.getAllSparePart(query);
        }

        //添加已经使用了的数据
        List<SpareItemDTO> result = spareItemDtos.stream().peek(t -> {
            BigDecimal reduce = actualUseRepository
                    .findBySpareIdAndShelveIdAndBatchNumberAndProcessInstanceId(
                            t.getSpareId(),
                            t.getShelveId(),
                            t.getBatchNumber(),
                            processInstanceId)
                    .stream()
                    .map(HvEamActualUse::getNumber)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            t.setBeenUsing(reduce);
        }).collect(Collectors.toList());
        //退库数据修正
        return result.stream().peek(t -> {
            //默认当存在 processInstanceId 和 状态为 入库的 单子为processInstanceId所创建出来的退库单
            HeaderQueryDTO headerQueryDTO = new HeaderQueryDTO();
            headerQueryDTO.setProcessInstanceId(processInstanceId);
            //出库单
            headerQueryDTO.setInOut(1);
            //区分备件的退款单 还是油品的退库单
            headerQueryDTO.setTypeClass(type);
            //单子是完成的状态的
            headerQueryDTO.setComplete(false);
            //可能存在多个退库单
            List<HeaderDTO> headerList = storeHeaderService.getHeaderList(headerQueryDTO);
            //遍历每个退库单
            for (HeaderDTO headerDTO : headerList) {
                //获取每个退库单里的项目
                for (LineDTO lineDTO : headerDTO.getLineDTOS()) {
                    if (t.getShelveId().equals(lineDTO.getShelveId()) &&
                            t.getSpareId().equals(lineDTO.getSpareId()) &&
                            t.getBatchNumber().equals(lineDTO.getBatchNumber())) {
                        //数量相减
                        t.setNumber(t.getNumber().subtract(lineDTO.getNumber()));
                        //如果小于0就设置为0
                        if (t.getNumber().compareTo(BigDecimal.ZERO) < 0) {
                            t.setNumber(BigDecimal.ZERO);
                        }
                    }
                }
            }
        }).collect(Collectors.toList());

    }

    /**
     * 当数据相同时 数量相加
     *
     * @param item 根据 备件ID 库房ID 批次号 进行数据合并
     * @return 数据合并
     */
    private List<SpareItemDTO> mergeNumber(List<SpareItemDTO> item) {
        List<SpareItemDTO> newItem = new ArrayList<>();
        //当数据个数大于1时开始处理
        for (SpareItemDTO spareItemDTO : item) {
            Optional<SpareItemDTO> result =
                    newItem.stream().filter(t -> t.getShelveId().equals(spareItemDTO.getShelveId())
                            && t.getSpareId().equals(spareItemDTO.getSpareId())
                            && t.getBatchNumber().equals(spareItemDTO.getBatchNumber())).findFirst();
            if (result.isPresent()) {
                result.get().setNumber(spareItemDTO.getNumber().add(result.get().getNumber()));
            } else {
                newItem.add(spareItemDTO);
            }
        }
        return newItem;
    }

}

