package com.hvisions.eam.controller;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.dto.lub.LubToShelveDTO;
import com.hvisions.eam.query.lub.LubToShelveQueryDTO;
import com.hvisions.eam.service.lub.LubToShelveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * <p>Title:LubToShelveController</p>
 * <p>Description:油品库存关系</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@RequestMapping(value = "/lubToShelve")
@Api(description = "油品库存关系")
@Slf4j
public class LubToShelveController {

    /**
     * 油品库存关系service
     */
    private final LubToShelveService lubToShelveService;

    @Autowired
    public LubToShelveController(LubToShelveService lubToShelveService) {
        this.lubToShelveService = lubToShelveService;
    }

    /**
     * 增加油品库存关系
     *
     * @param lubToShelveDTO 油品库存关系DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加油品库存关系")
    @PostMapping(value = "/createLubToShelve")
    public Integer createLubToShelve(@RequestBody LubToShelveDTO lubToShelveDTO) {
        if (lubToShelveService.isExist(lubToShelveDTO)) {
            //当前数据已存在,请修改对应数据
            throw new BaseKnownException(StoreExceptionEnum.THE_CURRENT_DATA_ALREADY_EXISTS_PLEASE_MODIFY_THE_CORRESPONDING_DATA);
        }
        return lubToShelveService.save(lubToShelveDTO);
    }

    /**
     * 通过ID修改油品库存关系
     *
     * @param lubToShelveDTO 油品库存关系DTO
     * @return 修改数据的ID
     */
    @ApiOperation(value = "通过ID修改油品库存关系")
    @PutMapping(value = "/updateLubToShelve")
    public Integer updateLubToShelve(@RequestBody LubToShelveDTO lubToShelveDTO) {
        //比较原油品Id 和当前油品Id  不相等不可以更新
        if (!lubToShelveService.compareLubId(lubToShelveDTO)) {
            throw new BaseKnownException(StoreExceptionEnum.LUBRICATING_IS_NOT_ALLOWED_TO_BE_MODIFIED);
        }
        return lubToShelveService.save(lubToShelveDTO);
    }

    /**
     * 查询油品库存关系 通过ID
     *
     * @param id 油品库存关系DTO
     * @return 关系数据
     */
    @ApiOperation(value = "查询油品库存关系 通过ID")
    @GetMapping(value = "/getLubToShelveById/{id}")
    public LubToShelveDTO getLubToShelveById(@PathVariable Integer id) {
        return lubToShelveService.findById(id);
    }

    /**
     * 删除关系表通过ID
     *
     * @param id 关系ID
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteLubToShelveById/{id}")
    public void deleteLubToShelveById(@PathVariable Integer id) {
        lubToShelveService.deleteById(id);
    }

    /**
     * 查询油品总数量 通过油品ID
     *
     * @param lubId 油品ID
     * @return 总数量
     */
    @ApiOperation(value = "查询油品总数量 通过油品ID")
    @PostMapping(value = "/getSumLubNumByLubId/{lubId}")
    public BigDecimal getSumLubNumByLubId(@PathVariable Integer lubId) {
        return lubToShelveService.sumLubNumByLubId(lubId);
    }

    /**
     * 查询全部 通过 油品编码 油品名称 库房编码 库房名称 批次号
     *
     * @param lubToShelveQueryDTO 关系表分页
     * @return 全部
     */
    @Deprecated
    @ApiOperation(value = "查询全部 通过 油品编码 油品名称 库房编码 库房名称 批次号")
    @PostMapping(value = "/findAllByLubCodeAndLubNameAndShelveCodeAndShelveNameAndBatchNumber")
    public Page<LubToShelveQueryDTO> findAllByLubCodeAndLubNameAndShelveCodeAndShelveNameAndBatchNumber(@RequestBody LubToShelveQueryDTO lubToShelveQueryDTO) {
        return lubToShelveService.findAllByLubCodeAndLubNameAndShelveCodeAndShelveNameAndBatchNumber(lubToShelveQueryDTO);
    }

    /**
     * 查询全部 通过 油品编码 油品名称 库房编码 库房名称 批次号
     *
     * @param lubToShelveQueryDTO 关系表分页
     * @return 全部
     */
    @ApiOperation(value = "查询全部 通过 油品编码 油品名称 库房编码 库房名称 批次号")
    @PostMapping(value = "/getLubToShelve")
    public Page<LubToShelveDTO> getLubToShelve(@RequestBody LubToShelveQueryDTO lubToShelveQueryDTO) {
        return lubToShelveService.getLubToShelve(lubToShelveQueryDTO);
    }
}