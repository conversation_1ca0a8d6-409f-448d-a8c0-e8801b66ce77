package com.hvisions.eam.repository.publicstore;

import com.hvisions.eam.entity.publicstore.HvEamOutboundCargo;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <p>Title:OutboundCargoRepository</p>
 * <p>Description:出库表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/15</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface OutboundCargoRepository extends JpaRepository<HvEamOutboundCargo, Integer> {

    /**
     * 通过流程ID获取出库表信息
     * @param processInstanceId 流程ID
     * @return 出库表信息
     */
    HvEamOutboundCargo findByProcessInstanceId(String processInstanceId);

}
