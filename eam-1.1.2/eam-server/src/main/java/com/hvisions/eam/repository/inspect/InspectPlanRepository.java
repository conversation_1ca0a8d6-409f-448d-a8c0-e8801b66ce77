package com.hvisions.eam.repository.inspect;

import com.hvisions.eam.entity.inspect.HvEamInspectPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: InspectPlanRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/28</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface InspectPlanRepository extends JpaRepository<HvEamInspectPlan, Integer> {


    /**
     * 根据timerId获取所有已经启用的点巡检计划
     *
     * @param id           timerId
     * @param conditionIds 计划状态idList
     * @return 点检计划List
     */
    List<HvEamInspectPlan> findAllByTimerIdAndStartUsingIsTrueAndAndInspectPlanConditionIdIn(Integer id, List<Integer> conditionIds);

    /**
     * 计划code是否存在
     *
     * @param inspectPlanNum 点巡检计划编号
     * @return 是否
     */
    boolean existsByInspectPlanNum(String inspectPlanNum);

    /**
     * 根据计划编号查询计划
     *
     * @param s 计划编码
     * @return 计划实体
     */
    HvEamInspectPlan findByInspectPlanNum(String s);

    /**
     * 根据timerId查绑定次数
     *
     * @param timerId timerId
     * @return 计划列表
     */
    List<HvEamInspectPlan> findAllByTimerId(Integer timerId);
}
