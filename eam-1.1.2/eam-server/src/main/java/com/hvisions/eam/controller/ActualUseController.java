package com.hvisions.eam.controller;

import com.hvisions.eam.activiti.lub.ItemsLubDTO;
import com.hvisions.eam.activiti.sapre.ItemsSpareDTO;
import com.hvisions.eam.dto.publicstore.ActualUseDTO;
import com.hvisions.eam.dto.publicstore.ApplyDTO;
import com.hvisions.eam.query.publicstore.ActualUseQueryDTO;
import com.hvisions.eam.service.publicstore.ActualUseService;
import com.hvisions.eam.dto.spare.SpareItemDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title:ActualUseController</p>
 * <p>Description:备件实际使用列表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@RequestMapping(value = "/ActualUseController")
@Api(description = "备件实际使用列表")
@Slf4j
public class ActualUseController {

    /**
     * 实际使用jpa
     */
    private final ActualUseService actualUseService;


    @Autowired
    public ActualUseController(ActualUseService actualUseService) {
        this.actualUseService = actualUseService;
    }

    /**
     * 批量增加备件实际使用列表
     *
     * @param actualUseDTOs 使用列表DTO List 集合
     * @return id集合
     */
    @ApiOperation(value = "批量增加备件实际使用列表")
    @PostMapping(value = "/createActualUseList")
    public List<Integer> createActualUseList(@RequestBody List<ActualUseDTO> actualUseDTOs) {
        return actualUseService.createActualUseList(actualUseDTOs);
    }

    /**
     * 修改备件实际使用列表
     *
     * @param actualUseDTO 使用清单DTO列表
     * @return id
     */
    @ApiOperation(value = "修改备件实际使用列表")
    @PostMapping(value = "/updateActualUse")
    public Integer updateActualUse(@RequestBody ActualUseDTO actualUseDTO) {
        return actualUseService.update(actualUseDTO);
    }

    /**
     * 通过id删除备件实际使用列表
     *
     * @param id id
     */
    @ApiOperation(value = "通过id删除备件实际使用列表")
    @GetMapping(value = "/deleteById/{id}")
    public void deleteById(@PathVariable Integer id) {
        actualUseService.deleteById(id);
    }

    /**
     * 通过Id list批量删除
     *
     * @param ids id
     */
    @ApiOperation(value = "通过Id list批量删除")
    @DeleteMapping(value = "/deleteByListId")
    public void deleteByListId(@RequestBody List<Integer> ids) {
        actualUseService.deleteByListId(ids);
    }

    /**
     * 通过 processInstance identifierKey type获取当前的流程所实际使用的备件
     *
     * @param actualUseQueryDTO 关联id
     * @return 分页
     */
    @ApiOperation(value = "查询备件实际申请(返回分页)")
    @PostMapping(value = "/getSpareActual")
    public Page<ActualUseDTO> getSpareActual(@RequestBody ActualUseQueryDTO actualUseQueryDTO) {
        actualUseQueryDTO.setType(1);
        return actualUseService.getActual(actualUseQueryDTO);
    }

    /**
     * 通过 processInstance identifierKey type获取当前的流程所实际使用的备件
     *
     * @param actualUseQueryDTO 关联id
     * @return 分页
     */
    @ApiOperation(value = "查询油品实际申请(返回分页)")
    @PostMapping(value = "/getLubActual")
    public Page<ActualUseDTO> getLubActual(@RequestBody ActualUseQueryDTO actualUseQueryDTO) {
        actualUseQueryDTO.setType(2);
        return actualUseService.getActual(actualUseQueryDTO);
    }

    //----------------------------------------------= 过期 =----------------------------------

    /**
     * 增加备件实际使用列表
     *
     * @param actualUseDTO 使用列表DTO
     * @return id
     */
    @Deprecated
    @ApiOperation(value = "增加备件实际使用列表")
    @PostMapping(value = "/createActualUse")
    public Integer createActualUse(@RequestBody ActualUseDTO actualUseDTO) {
        return actualUseService.create(actualUseDTO);
    }


    /**
     * 获取备件全部出库的申请项
     *
     * @param actualUseQueryDTO 查询条件
     * @return 全部信息
     */
    @ApiOperation(value = "获取备件全部申请项（Activiti）")
    @PostMapping(value = "/getSpareApplyThrough")
    public List<ActualUseDTO> getSpareApplyThrough(@RequestBody ActualUseQueryDTO actualUseQueryDTO) {
        //equipment-maintenance store-spare
        return actualUseService.spareApplyThrough(actualUseQueryDTO, "store-spare");
    }

    /**
     * 获取备件全部出库的申请项-维修（Activiti）
     *
     * @param actualUseQueryDTO 查询条件
     * @return 全部信息
     */
    @Deprecated
    @ApiOperation(value = "获取备件全部申请项-维修（Activiti）")
    @PostMapping(value = "/getSpareApplyThroughEquipment")
    public List<ActualUseDTO> getSpareApplyThroughEquipment(@RequestBody ActualUseQueryDTO actualUseQueryDTO) {
        //equipment-maintenance store-spare
        return actualUseService.spareApplyThrough(actualUseQueryDTO, "store-spare");
    }

    /**
     * 获取油品全部申请项
     *
     * @param actualUseQueryDTO 查询条件
     * @return 全部信息
     */
    @Deprecated
    @ApiOperation(value = "获取油品全部申请项（Activiti）")
    @PostMapping(value = "/getLubApplyThrough")
    public List<ActualUseDTO> getLubApplyThrough(@RequestBody ActualUseQueryDTO actualUseQueryDTO) {
        return actualUseService.lubApplyThrough(actualUseQueryDTO, "store-lub");
    }

    /**
     * 获取油品全部申请项
     *
     * @param actualUseQueryDTO 查询条件
     * @return 全部信息
     */
    @Deprecated
    @ApiOperation(value = "获取油品全部申请项-维修（Activiti）")
    @PostMapping(value = "/getLubApplyThroughEquipment")
    public List<ActualUseDTO> getLubApplyThroughEquipment(@RequestBody ActualUseQueryDTO actualUseQueryDTO) {
        return actualUseService.lubApplyThrough(actualUseQueryDTO, "store-lub");
    }

    /**
     * 通过BusinessKey获取备件申请表 （已申请项）
     *
     * @param businessKey 唯一标识符
     * @return 申请单list
     */
    @ApiOperation(value = "通过BusinessKey获取备件申请表")
    @GetMapping(value = "/getSpareApply/{businessKey}")
    public List<ApplyDTO> getSpareApply(@PathVariable String businessKey) {
        return actualUseService.getSpareOrLubApply(businessKey, 1);
    }


    /**
     * 通过BusinessKey获取油品申请表
     *
     * @param businessKey 唯一标识符
     * @return 申请单list
     */
    @ApiOperation(value = "通过BusinessKey获取油品申请表")
    @GetMapping(value = "/getLubApply/{businessKey}")
    public List<ApplyDTO> getLubApply(@PathVariable String businessKey) {
        return actualUseService.getSpareOrLubApply(businessKey, 2);
    }

    /**
     * 通过BusinessKey获取备件申请通过的申请项数据
     *
     * @param businessKey 唯一标识符
     * @return 申请项 list
     */
    @Deprecated
    @ApiOperation(value = "通过BusinessKey获取全部申请通过的申请项数据")
    @GetMapping(value = "/getSpareApplyList/{businessKey}")
    public List<ItemsSpareDTO> getSpareApplyList(@PathVariable String businessKey) {
        return actualUseService.getSpareApplyList(businessKey);
    }

    /**
     * 通过BusinessKey获取油品申请通过的申请项数据
     *
     * @param businessKey 唯一标识符
     * @return 申请项 list
     */
    @Deprecated
    @ApiOperation(value = "通过BusinessKey获取油品申请通过的申请项数据")
    @GetMapping(value = "/getLubApplyList/{businessKey}")
    public List<ItemsLubDTO> getLubApplyList(@PathVariable String businessKey) {
        return actualUseService.getLubApplyList(businessKey);
    }

    /**
     * 通过processInstanceIds 获取实际使用获取油品申请通过的申请项数据
     *
     * @param processInstanceIds 流程实例
     * @return 申请项 list
     */
    @ApiOperation(value = "通过processInstanceIds 获取实际使用获取油品申请通过的申请项数据")
    @PostMapping(value = "/getActualUseByProcessInstanceIds")
    public List<ActualUseDTO> getActualUseByProcessInstanceIds(@RequestBody List<String> processInstanceIds) {
        return actualUseService.getActualUseByProcessInstanceIds(processInstanceIds);
    }


    /**
     * 通过businessKey 获取备件申请通过并出库的 备件
     *
     * @param processInstanceId 流程实例id
     * @return 申请通过并出库 可以使用的备件
     */
    @ApiOperation("通过businessKey 获取备件申请通过并出库的 备件")
    @GetMapping({"/getSpareCanUse/{processInstanceId}"})
    public List<SpareItemDTO> getSpareCanUse(@PathVariable String processInstanceId) {
        return this.actualUseService.getSpareOrLubCanUse(processInstanceId, 1);
    }

    /**
     * 通过businessKey 获取备件申请通过并出库的 油品
     *
     * @param processInstanceId 流程实例id
     * @return 申请通过并出库 可以使用的油品
     */
    @ApiOperation("通过businessKey 获取备件申请通过并出库的 油品")
    @GetMapping({"/getLubCanUse/{processInstanceId}"})
    public List<SpareItemDTO> getLubCanUse(@PathVariable String processInstanceId) {
        return this.actualUseService.getSpareOrLubCanUse(processInstanceId, 2);
    }


}