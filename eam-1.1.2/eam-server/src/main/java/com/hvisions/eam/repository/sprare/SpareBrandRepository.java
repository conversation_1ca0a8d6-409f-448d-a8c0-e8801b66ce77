package com.hvisions.eam.repository.sprare;

import com.hvisions.eam.entity.spare.HvEamSpareBrand;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: SpareBrandRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/5/11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface SpareBrandRepository extends JpaRepository<HvEamSpareBrand, Integer> {

    /**
     * 根据品牌编码查询
     *
     * @param code 品牌编码
     * @return 品牌信息
     */
    HvEamSpareBrand getAllBySpareBrandCode(String code);

    /**
     * 根据品牌名称查询
     *
     * @param name 品牌编码
     * @return 品牌信息
     */
    HvEamSpareBrand findBySpareBrandName(String name);

}