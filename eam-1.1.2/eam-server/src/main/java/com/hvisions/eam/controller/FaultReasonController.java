package com.hvisions.eam.controller;

import com.hvisions.eam.dto.fault.FaultReasonDTO;
import com.hvisions.eam.dto.fault.FaultReasonQueryDTO;
import com.hvisions.eam.service.fault.FaultReasonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;


/**
 * <p>Title: HvEmFaultReasonController</p >
 * <p>Description: 故障原因controller</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/EmFaultReason")
@Api(description = "设备故障原因controller")
@Slf4j
public class FaultReasonController {

    /**
     * 故障原因service
     */
    private final FaultReasonService faultReasonService;

    @Autowired
    public FaultReasonController(FaultReasonService faultReasonService) {
        this.faultReasonService = faultReasonService;
    }

    /**
     * 新增故障原因
     *
     * @param faultReasonDTO 故障原因DTO
     * @return 故障原因id
     */
    @ApiOperation(value = "新增故障原因")
    @PostMapping(value = "/add")
    public Integer addFaultReason(@RequestBody FaultReasonDTO faultReasonDTO) {
        return faultReasonService.addOrUpdateFaultReason(faultReasonDTO);
    }

    /**
     * 删除故障原因
     *
     * @param id 故障原因id
     */
    @ApiOperation(value = "删除故障原因")
    @DeleteMapping(value = "/delete/{id}")
    public void deleteFaultReason(@PathVariable Integer id) {
        faultReasonService.deleteFaultReason(id);
    }


    /**
     * 通过故障编码删除故障原因
     *
     * @param reasonCode 故障编码
     */
    @ApiOperation(value = "通过故障编码删除故障原因")
    @DeleteMapping(value = "/deleteFaultReasonCode/{reasonCode}")
    public void deleteFaultReasonCode(@PathVariable String reasonCode) {
        faultReasonService.deleteFaultReasonCode(reasonCode);
    }

    /**
     * 更新故障原因
     *
     * @param faultReasonDTO 故障原因DTO
     */
    @ApiOperation(value = "更新故障原因")
    @PutMapping(value = "/update")
    public void updateFaultReason(@RequestBody FaultReasonDTO faultReasonDTO) {
        faultReasonService.addOrUpdateFaultReason(faultReasonDTO);
    }

    /**
     * 查询故障原因 表现找原因
     *
     * @param faultReasonQueryDTO 故障查询故障原因DTO
     * @return 故障原因集合
     */
    @ApiOperation(value = "查询故障原因")
    @PostMapping(value = "/getFaultReason")
    public Page<FaultReasonDTO> getFaultReason(@RequestBody FaultReasonQueryDTO faultReasonQueryDTO) {
        return faultReasonService.getFaultReasonByFaultId(faultReasonQueryDTO);
    }

    /**
     * 根据id查询故障原因
     *
     * @param id 故障原因id
     * @return 故障原因
     */
    @ApiOperation(value = "根据id查询故障原因")
    @GetMapping(value = "/getFaultReasonById/{id}")
    public FaultReasonDTO getFaultReasonById(@PathVariable Integer id) {
        return faultReasonService.getFaultReasonById(id);
    }

    /**
     * 通过故障原因编码获取故障原因
     *
     * @param faultReasonCode 编码
     * @return 故障原因
     */
    @ApiOperation(value = "通过编码获取故障原因")
    @GetMapping(value = "/getFaultReasonByCode/{faultReasonCode}")
    public FaultReasonDTO getFaultReasonByCode(@PathVariable String faultReasonCode) {
        return faultReasonService.getFaultReasonByCode(faultReasonCode);
    }

}