package com.hvisions.eam.configuration.inspect;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: TopicRabbitConfig</p>
 * <p>Description:话题模式消息队列 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Configuration
public class InspectTopicRabbitConfig {
    /**
     *消息队列Queue名称
     */
    final static String EQUIPMENT_INSPECT_QUEUE_NAME = "equipment-inspect";
    /**
     * timer-exchange名称
     */
    private final static String TIMER_EXCHANGE_NAME = "timer-exchange";

    @Bean
    public Queue equipmentInspect() {
        return new Queue(InspectTopicRabbitConfig.EQUIPMENT_INSPECT_QUEUE_NAME);
    }


    @Bean
    TopicExchange exchange() {
        return new TopicExchange(TIMER_EXCHANGE_NAME);
    }

    /**
     * 绑定队列和关注的消息
     *
     * @param equipmentInspect      队列
     * @param exchange 分发器
     * @return 绑定
     */
    @Bean
    Binding bindingExchangeMessage(Queue equipmentInspect, TopicExchange exchange) {
        return BindingBuilder.bind(equipmentInspect).to(exchange).with("timer.simple");
    }

}
