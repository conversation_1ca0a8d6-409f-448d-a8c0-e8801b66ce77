package com.hvisions.eam.repository.publicstore;

import com.hvisions.eam.entity.HvEamSerialNumber;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title:SerialNumberRepository</p>
 * <p>Description:流水号</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/15</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface SerialNumberRepository extends JpaRepository<HvEamSerialNumber, Integer> {

    /**
     * 通过唯一标识符（推荐服务名）来获取流水号信息
     *
     * @param service 唯一标识符（推荐服务名）
     * @return 流水号信息
     */
    HvEamSerialNumber findByService(String service);

}
