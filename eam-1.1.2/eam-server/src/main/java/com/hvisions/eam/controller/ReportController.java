package com.hvisions.eam.controller;

import com.hvisions.eam.dto.publicstore.SparePartInfo;
import com.hvisions.eam.service.publicstore.SparePartReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title: ReportController</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2022/5/16</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping("/sparePartReport")
@Api(description = "备件相关的报告")
public class ReportController {
    private final SparePartReportService sparePartReportService;
    @Autowired
    public ReportController(SparePartReportService sparePartReportService) {
        this.sparePartReportService = sparePartReportService;
    }

    /**
     * 获取设备使用的备件信息
     * @param equipmentId 设备id
     * @return 备件统计数据
     */
    @ApiOperation("获取设备使用的备件信息")
    @GetMapping("/equipmentSparePart")
    public List<SparePartInfo> equipmentSparePart(@RequestParam Integer equipmentId) {
        return sparePartReportService.equipmentSparePart(equipmentId);
    }
}









