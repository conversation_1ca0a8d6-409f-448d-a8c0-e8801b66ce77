package com.hvisions.eam.controller;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dao.UtilsMapper;
import com.hvisions.framework.client.UserClient;
import com.hvisions.framework.dto.user.UserDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>Title: UtilsController</p>
 * <p>Description: 特殊的一些查询接口</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/7/2</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/utils")
@Api(description = "通用方法")
@Slf4j
public class UtilsController {
    @Autowired
    UserClient userClient;
    @Autowired
    UtilsMapper mapper;

    @GetMapping(value = "/getDefaultUser")
    @ApiOperation(value = "获取设备对应的用户信息")
    public UserDTO getDefaultRepaireUser(Integer equipmentId) {
        Integer userId;
        try {
            userId = mapper.getUserId(equipmentId);
            if (userId == null) {
                return null;
            }
        } catch (Exception ex) {
            log.info("查询异常:{}", ex.getMessage());
            return null;
        }
        ResultVO<UserDTO> result = userClient.getUser(userId);
        if (!result.isSuccess()) {
            log.info("用户接口发生异常：{}", result.getMessage());
            return null;
        }
        return result.getData();
    }
}









