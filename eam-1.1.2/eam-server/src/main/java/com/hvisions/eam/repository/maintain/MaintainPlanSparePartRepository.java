package com.hvisions.eam.repository.maintain;

import com.hvisions.eam.entity.maintain.HvEamMaintainPlanSparePart;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: MaintainProcessDataRepository</p>
 * <p>Description: 保养数据保存仓储对象</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/10/14</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaintainPlanSparePartRepository extends JpaRepository<HvEamMaintainPlanSparePart, Integer> {
    /**
     * 删除之前的配置
     *
     * @param planId 计划id
     */
    void deleteAllByMaintainPlanId(Integer planId);


    /**
     * 根据计划查询备件信息
     *
     * @param planId 计划id
     * @return 计划备件信息
     */
    List<HvEamMaintainPlanSparePart> findAllByMaintainPlanId(Integer planId);
}









