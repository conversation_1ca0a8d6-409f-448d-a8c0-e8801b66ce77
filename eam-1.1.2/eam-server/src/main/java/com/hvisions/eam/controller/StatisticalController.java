package com.hvisions.eam.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.hvisions.eam.dto.autonomy.*;
import com.hvisions.eam.entity.autonomy.HvAmAutonomyMaintenanceProcessItem;
import com.hvisions.eam.service.autonomy.StatisticalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-24
 */
@Api(tags = "统计")
@RestController
@RequestMapping("/statistical")
public class StatisticalController {

    private final StatisticalService statisticalService;

    @Autowired
    public StatisticalController(StatisticalService statisticalService) {
        this.statisticalService = statisticalService;
    }

    /**
     * 计划执行信息-分页查询
     * <AUTHOR>

     * @param dto 查询入参
     * @return 分页对象
     */
    @ApiOperation(value = "计划执行信息-分页查询")
    @PostMapping("/queryProcessDataList")
    public Page<MaintenanceProcessDataDTO> queryProcessDataList(@RequestBody @Valid MaintenanceProcessDataQueryDTO dto) {
        return statisticalService.queryProcessDataList(dto);
    }

    /**
     * 检查统计-分页查询
     * <AUTHOR>

     * @param dto 软
     * @return 分页对象
     */
    @ApiOperation(value = "检查统计-分页查询")
    @PostMapping("/queryList")
    public R<Map<String,Object>> queryProcessItemList(@RequestBody @Valid MaintenanceProcessItemQueryDTO dto) {
        Map<String, Object> map = statisticalService.queryProcessItemList(dto);
        return R.ok(map);
    }

    /**
     * 检查统计-错误信息
     * <AUTHOR>

     * @param dto 入参
     * @return 错误信息
     */
    @ApiOperation(value = "检查统计-错误信息")
    @PostMapping("/queryCheckAbnormalInfo")
    public List<CheckAbnormalInfoDTO> checkAbnormalInfo(@RequestBody @Valid CheckAbnormalInfoQueryDTO dto) {
        return statisticalService.checkAbnormalInfo(dto);
    }

    @ApiOperation(value = "模糊查询项目")
    @PostMapping("/fuzzyQueryItem")
    public List<HvAmAutonomyMaintenanceProcessItem> fuzzyQueryItem(@RequestBody FuzzyQueryItemDTO dto) {
        return statisticalService.fuzzyQueryItem(dto.getKeyword());
    }

}