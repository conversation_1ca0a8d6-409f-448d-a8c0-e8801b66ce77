package com.hvisions.eam.repository.fault;

import com.hvisions.eam.entity.fault.HvEmFaultReason;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;


/**
 * <p>Title: FaultReasonRepository</p >
 * <p>Description: 故障原因Repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface FaultReasonRepository extends JpaRepository<HvEmFaultReason, Integer> {

    /**
     * 根据故障id查询原因
     *
     * @param faultId  故障id
     * @param pageable 分页对象
     * @return 故障原因集合
     */
    Page<HvEmFaultReason> findByFaultIdEquals(Integer faultId, Pageable pageable);

    /**
     * 通过编码查询故障原因
     * @param reasonCode 故障原因编码
     * @return 故障原因
     */
    HvEmFaultReason findByReasonCode(String reasonCode);

    /**
     * 通过故障id查询故障原因
     * @param faultId 故障id
     * @return 故障原因
     */
    List<HvEmFaultReason> findByFaultId(Integer faultId);

}
