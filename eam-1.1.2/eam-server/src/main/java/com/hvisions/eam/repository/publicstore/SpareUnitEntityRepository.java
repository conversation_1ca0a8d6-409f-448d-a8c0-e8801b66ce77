package com.hvisions.eam.repository.publicstore;

import com.hvisions.eam.entity.publicstore.HvEamSpareUnit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title:SpareUnitEntityRepository</p>
 * <p>Description:备件单位</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface SpareUnitEntityRepository extends JpaRepository<HvEamSpareUnit, Integer> {

    /**
     * 通过单位名称获取单位
     *
     * @param unitName 单位名称
     * @return 单位
     */
    HvEamSpareUnit findByUnitName(String unitName);
}
