package com.hvisions.eam.repository.maintain;

import com.hvisions.eam.entity.maintain.HvEamMaintainProcessData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: MaintainProcessDataRepository</p>
 * <p>Description: 保养数据保存仓储对象</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/10/14</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaintainProcessDataRepository extends JpaRepository<HvEamMaintainProcessData, Integer> {
    /**
     * 查询是否已经存在数据
     *
     * @param id 流程实例id
     * @return 流程实例id
     */
    boolean existsByProcessInstanceId(String id);

}









