package com.hvisions.eam.repository.maintain;

import com.hvisions.eam.entity.maintain.HvEamItemSparePart;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: ItemSparePartRepository</p >
 * <p>Description: 保养项目-备件Repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/22</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface ItemSparePartRepository extends JpaRepository<HvEamItemSparePart, Integer> {
    /**
     * 根据保养项目id查询油品备件信息
     *
     * @param maintainItemId 保养项目id
     * @return 项目-备件集合
     */
    List<HvEamItemSparePart> getAllByMaintainItemId(Integer maintainItemId);

    /**
     * 根据保养项目id查询油品备件信息
     *
     * @param maintainItemIdList 保养项目id列表
     * @return 项目-备件集合
     */
    List<HvEamItemSparePart> getAllByMaintainItemIdIn(List<Integer> maintainItemIdList);

    /**
     * 根据保养项目id删除项目与备件的关系
     *
     * @param maintainItemId 保养项目id
     */
    void deleteAllByMaintainItemId(Integer maintainItemId);
}
