package com.hvisions.eam.controller;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.eam.dto.maintain.MaintainDrawingBoardDTO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalDTO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalQueryDTO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalTimeQuantumDTO;
import com.hvisions.eam.enums.MaintainErrorExceptionEnum;
import com.hvisions.eam.service.maintain.MaintainDataService;
import com.hvisions.eam.service.maintain.MaintainStatisticalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: MaintainStatisticalController</p >
 * <p>Description: 保养统计</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/4</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/maintainStatistical")
@Api(description = "保养统计接口")
public class MaintainStatisticalController {
    private final MaintainStatisticalService maintainStatisticalService;
    private final MaintainDataService dataService;

    @Autowired
    public MaintainStatisticalController(MaintainStatisticalService maintainStatisticalService, MaintainDataService dataService) {
        this.maintainStatisticalService = maintainStatisticalService;
        this.dataService = dataService;
    }


    /**
     * 获取保养统计数据
     *
     * @param maintainStatisticalQueryDTO 查询条件
     * @return list
     */
    @ApiOperation(value = "获取保养统计数据")
    @PostMapping(value = "/getMaintainStatistical")
    public List<MaintainStatisticalDTO> getMaintainStatistical(@RequestBody MaintainStatisticalQueryDTO maintainStatisticalQueryDTO) {
        return maintainStatisticalService.getMaintainStatistical(maintainStatisticalQueryDTO);
    }

    /**
     * 今日保养次数
     *
     * @return 今日保养次数
     */
    @ApiOperation(value = "今日保养次数")
    @GetMapping(value = "getTodayMaintainCount")
    public Integer getTodayMaintainCount() {
        return maintainStatisticalService.getTodayMaintainCount();
    }


    /**
     * 获取本周保养次数
     *
     * @return 次数
     */
    @ApiOperation(value = "获取本周保养次数")
    @GetMapping(value = "getWeekCount")
    public Integer getWeekCount() {
        return maintainStatisticalService.getWeekCount();
    }

    /**
     * 根据时间段查询保养次数
     *
     * @param maintainStatisticalQueryDTO 查询条件
     * @return list
     */
    @ApiOperation(value = "根据时间段查询保养次数")
    @PostMapping(value = "/getCountByTimeQuantum")
    public List<MaintainStatisticalTimeQuantumDTO> getCountByTimeQuantum(@RequestBody MaintainStatisticalQueryDTO maintainStatisticalQueryDTO) {
        return maintainStatisticalService.getCountByTimeQuantum(maintainStatisticalQueryDTO);
    }

    /**
     * 获取保养润滑看板
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 看板
     */
    @ApiOperation(value = "获取保养润滑看板")
    @GetMapping(value = "/getMaintainDrawingBoardDTO")
    public MaintainDrawingBoardDTO getMaintainDrawingBoardDTO(@RequestParam @ApiParam(value = "开始时间", example = "2017-01-01") String startTime,
                                                              @RequestParam @ApiParam(value = "结束时间", example = "2020-05-01") String endTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date begin, end;
        try {
            begin = simpleDateFormat.parse(startTime);
            end = simpleDateFormat.parse(endTime);
        } catch (Exception e) {
            throw new BaseKnownException(MaintainErrorExceptionEnum.START_END_TIME_EXCEPTION);
        }
        return maintainStatisticalService.getMaintainDrawingBoardDTO(begin, end);


    }

    /**
     * 保存流程数据
     *
     * @param processInstanceId 流程id
     */
    @PostMapping(value = "/saveProcessInstanceInfo")
    @ApiOperation(value = "保存流程数据")
    public void saveProcessInstanceInfo(@RequestParam String processInstanceId) {
        dataService.save(processInstanceId);
    }
}