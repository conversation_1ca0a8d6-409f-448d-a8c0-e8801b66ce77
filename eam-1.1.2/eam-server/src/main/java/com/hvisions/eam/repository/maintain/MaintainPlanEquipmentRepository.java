package com.hvisions.eam.repository.maintain;

import com.hvisions.eam.entity.maintain.HvEamMaintainPlanEquipment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: MaintainPlanEquipmentRepository</p >
 * <p>Description: 计划与设备关系</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/6/21</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaintainPlanEquipmentRepository extends JpaRepository<HvEamMaintainPlanEquipment, Integer> {
    /**
     * 根据计划id删除关系表
     *
     * @param planId 计划id
     */
    void deleteAllByMaintainPlanId(Integer planId);

    /**
     * 根据计划id查询关系表
     *
     * @param planIds 计划id列表
     * @return 关系实体List
     */
    List<HvEamMaintainPlanEquipment> getAllByMaintainPlanIdIn(List<Integer> planIds);
    /**
     * 根据计划id查询关系表
     *
     * @param planId 计划id
     * @return 关系实体List
     */
    List<HvEamMaintainPlanEquipment> getAllByMaintainPlanId(Integer planId);

}
