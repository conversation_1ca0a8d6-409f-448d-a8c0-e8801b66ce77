package com.hvisions.eam.repository.inspect;

import com.hvisions.eam.entity.inspect.HvEamInspectItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: InspectItemRepository</p >
 * <p>Description: 点检项目repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/16</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface InspectItemRepository extends JpaRepository<HvEamInspectItem, Integer> {
    /**
     * itemCode是否存在
     *
     * @param inspectItemCode 点检项目code
     * @return 是否
     */
    boolean existsByInspectItemCode(String inspectItemCode);

    /**
     * 根据项目编码查询项目
     *
     * @param inspectItemCode 点检项目编码
     * @return 点检项目实体
     */

    HvEamInspectItem findByInspectItemCode(String inspectItemCode);

    /**
     * 获取所有循环信息
     *
     * @return 循环信息
     */
    @Query(value = "select distinct cycle from hv_eam_inspect_item ",nativeQuery = true)
    List<String> getAllCycle();
}
