package com.hvisions.eam.repository.inspect;

import com.hvisions.eam.entity.inspect.HvEamInspectPlanItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: InspectPlanItemRepository</p>
 * <p>Description: 点巡检计划和点巡检项目关系表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/5/19</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface InspectPlanItemRepository extends JpaRepository<HvEamInspectPlanItem,Integer> {
    /**
     * 是否已经被使用
     * @param itemId 点巡检计划id
     * @return 点巡检项目是否已经被使用
     */
    Boolean existsByInspectItemId(Integer itemId);

    /**
     * 根据计划查询所有的点巡检项目
     * @param planId 点巡检计划id
     * @return 点巡检项目列表
     */
    List<HvEamInspectPlanItem> findAllByInspectPlanId(Integer planId);
    /**
     * 根据计划查询所有的点巡检项目
     * @param planId 点巡检计划id
     * @return 点巡检项目列表
     */
    List<HvEamInspectPlanItem> findAllByInspectPlanIdIn(List<Integer> planId);
    /**
     * 删除计划之前的数据
     * @param planId 计划id
     */
    void deleteAllByInspectPlanId(Integer planId);


}

    
    
    
    