package com.hvisions.eam.controller;

import com.hvisions.eam.dto.publicstore.MatchingDTO;
import com.hvisions.eam.service.publicstore.MatchingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title:MatchingController</p>
 * <p>Description:正则匹配规则</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/21</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@RequestMapping(value = "/matchingController")
@Api(description = "正则匹配规则")
@Slf4j
public class MatchingController {

    private final MatchingService matchingService;

    @Autowired
    public MatchingController(MatchingService matchingService) {
        this.matchingService = matchingService;
    }

    /**
     * 增加 和 修改匹配规则
     *
     * @param matchingDTO 匹配规则DTO
     * @return id
     */
    @ApiOperation(value = "增加 和 修改匹配规则")
    @PostMapping(value = "/createMatching")
    public Integer createMatching(@RequestBody MatchingDTO matchingDTO) {
        return matchingService.createMatching(matchingDTO);
    }

    /**
     * 通过服务名查询匹配规则
     *
     * @param service 匹配规则DTO
     * @return id
     */
    @ApiOperation(value = "通过服务名查询匹配规则")
    @PostMapping(value = "/getMatchingByService/{service}")
    public MatchingDTO getMatchingByService(@PathVariable String service) {
        return matchingService.getMatchingByService(service);
    }

    /**
     * 获取全部匹配规则服务名
     *
     * @return 匹配规则
     */
    @ApiOperation(value = "获取全部匹配规则服务名")
    @PostMapping(value = "/getServiceName")
    public List<String> getServiceName() {
        return matchingService.getServiceName();
    }

    /**
     * 删除匹配规则
     *
     * @param id id
     */
    @ApiOperation(value = " 删除匹配规则 ")
    @DeleteMapping(value = "/deleteById/{id}")
    public void deleteById(@PathVariable Integer id) {
        matchingService.deleteMatching(id);
    }
}