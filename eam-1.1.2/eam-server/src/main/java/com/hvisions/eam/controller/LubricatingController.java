package com.hvisions.eam.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.configuration.spare.StoreConfig;
import com.hvisions.eam.dto.lub.LubExtendDTO;
import com.hvisions.eam.dto.lub.LubricatingDTO;
import com.hvisions.eam.query.lub.LubricatingQueryDTO;
import com.hvisions.eam.service.lub.LubExtendService;
import com.hvisions.eam.service.lub.LubService;
import com.hvisions.eam.consts.SpareConsts;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: SpareController</p>
 * <p>Description: 油品</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 * <p>
 * 油品控制类
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/lub")
@Api(description = "油品")
@Slf4j
public class LubricatingController {

    /**
     * 油品service
     */
    private final LubService lubService;

    /**
     * 油品导出导入service
     */
    private final LubExtendService lubExtendService;

    @Autowired
    public LubricatingController(LubService lubService,
                                 LubExtendService lubExtendService) {
        this.lubService = lubService;
        this.lubExtendService = lubExtendService;
    }


    /**
     * 增加油品
     *
     * @param lubricatingDTO 油品DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加油品")
    @PostMapping(value = "/createLub")
    public Integer createLub(@RequestBody LubricatingDTO lubricatingDTO) {
        return lubService.save(lubricatingDTO);
    }

    /**
     * 修改油品 通过油品DTO
     *
     * @param lubricatingDTO 油品DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "修改油品")
    @PutMapping(value = "/updateLub")
    public Integer updateLub(@RequestBody LubricatingDTO lubricatingDTO) {
        return lubService.save(lubricatingDTO);
    }

    /**
     * 查询油品 通过ID
     *
     * @param id 油品DTO
     * @return 油品
     */
    @ApiOperation(value = "查询油品 通过ID")
    @PostMapping(value = "/getLubById/{id}")
    public LubricatingDTO getLubById(@PathVariable Integer id) {
        return lubService.findById(id);
    }

    /**
     * 查询油品 通过编码
     *
     * @param code 油品编号
     * @return 油品数据
     */
    @ApiOperation(value = "查询油品 通过编码")
    @PostMapping(value = "/getLubByCode/{code}")
    public LubricatingDTO getLubByCode(@PathVariable String code) {
        return lubService.getLubByCode(code);
    }
    /**
     * 查询油品 通过编码列表
     *
     * @param codes 油品编码列表
     * @return 油品
     */
    @ApiOperation(value = "查询油品 通过编码列表")
    @PostMapping(value = "/getLubByCodeList")
    public List<LubricatingDTO> getLubByCodeList(@RequestBody List<String> codes) {
        return lubService.getLubByCodeList(codes);
    }

    /**
     * 通过ID删除油品
     *
     * @param id LubId
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteLubById/{id}")
    public void deleteLubById(@PathVariable Integer id) {
        lubService.deleteById(id);
    }

    /**
     * 分页查询
     * 通过 油品编码 油品名称 小类ID 是否关键部位  联合查询
     *
     * @param lubricatingQueryDTO 油品分页DTO
     * @return 分页信息
     */
    @ApiOperation(value = "分页查询 通过 油品编码 油品名称 类型ID 是否关键部位  联合查询")
    @PostMapping(value = "/getAllByLubCodeAndLubNameAndLubTypeSub")
    public Page<LubricatingDTO> getAllByLubCodeAndLubNameAndLubTypeSub(@RequestBody LubricatingQueryDTO lubricatingQueryDTO) {
        return lubService.findAllByLubCodeAndLubNameAndLubType(lubricatingQueryDTO);
    }

    /**
     * 通过id集合查询油品集合
     *
     * @param ids 油品分页DTO
     * @return 油品集合
     */
    @ApiOperation(value = "通过id集合查询油品集合")
    @PostMapping(value = "/getLubListByIdList")
    public List<LubricatingDTO> getLubListByIdList(@RequestBody List<Integer> ids) {
        return lubService.getLubListByIdList(ids);
    }

    /**
     * 导出所有油品信息 1
     *
     * @return 备件信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportLubLink")
    @ApiOperation(value = "导出所有油品信息,支持超链接")
    public ResponseEntity<byte[]> exportLubLink() throws IOException, IllegalAccessException {
        return lubExtendService.exportEquipmentLink();
    }

    /**
     * 导出所有油品信息 2
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportEquipment")
    @ApiOperation(value = "导出所有油品信息")
    public ResultVO<ExcelExportDto> exportEquipment() throws IOException, IllegalAccessException {
        return lubExtendService.exportEquipment();
    }

    /**
     * 获取导入模板 (文件流)
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getLubImportTemplateLink")
    @ApiOperation(value = "获取油品导入模板,支持超链接")
    public ResponseEntity<byte[]> getLubImportTemplateLink() throws IOException, IllegalAccessException {
        return ExcelUtil.generateImportFile(LubExtendDTO.class, SpareConsts.LUB_EXPORT_FILE_NAME, null);
    }


    /**
     * 获取导入模板  (Excel)
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getLubImportTemplate")
    @ApiOperation(value = "获取油品导入模板")
    public ResultVO<ExcelExportDto> getLubImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(LubExtendDTO.class, SpareConsts.LUB_EXPORT_FILE_NAME, null);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setFileName(SpareConsts.LUB_EXPORT_FILE_NAME);
        excelExportDto.setBody(result.getBody());
        return ResultVO.success(excelExportDto);
    }

    /**
     * 导入所有油品信息
     *
     * @param file 备件信息文档
     * @return a
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importEquipment")
    @ApiOperation(value = "导入油品信息，如果code存在则更新，code不存在则新增")
    public ImportResult importEquipment(@RequestParam("file") MultipartFile file)
            throws IllegalAccessException, ParseException, IOException {
        log.info("油品基础信息数据导入");
        return lubExtendService.importEquipment(file);
    }


    /**
     * 通过批次号 获取油品信息
     *
     * @param batchNumber 批次号
     * @return 备件信息
     */
    @ApiOperation(value = "通过批次号获取油品")
    @GetMapping(value = "/getLubricatingByBatchNumber/{batchNumber}")
    public LubricatingDTO getLubricatingByLubCode(@PathVariable String batchNumber) {
        //在油品中默认 服务名为 油品
        return lubService.getSpareDTOByBatchNumber(StoreConfig.LUB_NAME, batchNumber);
    }
}