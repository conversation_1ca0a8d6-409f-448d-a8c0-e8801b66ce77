package com.hvisions.eam.configuration.inspect;

import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.eam.dao.inspect.InspectDataMapper;
import com.hvisions.eam.service.inspect.InspectDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: InitialStartup</p >
 * <p>Description:用于同步所有工作流中没有同步的设备保养数据
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class InspectDataSyncStartUp extends SafetyCommandLineRunner {

    @Autowired
    InspectDataService service;

    @Autowired
    InspectDataMapper mapper;

    @Override
    public void callRunner(String... args) {
        List<String> processInstanceIds = mapper.getProcessInstanceIds();
        if (processInstanceIds != null) {
            for (String processInstanceId : processInstanceIds) {
                service.save(processInstanceId);
            }
        }
    }
}