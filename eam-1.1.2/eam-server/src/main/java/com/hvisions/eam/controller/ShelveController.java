package com.hvisions.eam.controller;

import com.hvisions.eam.dto.publicstore.ShelveDTO;
import com.hvisions.eam.service.publicstore.ShelveService;
import com.hvisions.eam.query.spare.ShelveQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title:ShelveController</p>
 * <p>Description:库房</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@RequestMapping(value = "/shelve")
@Api(description = "库房")
@Slf4j
public class ShelveController {

    private final ShelveService shelveService;

    @Autowired
    public ShelveController(ShelveService shelveService) {
        this.shelveService = shelveService;
    }

    /**
     * 通过ID修改库房信息
     *
     * @param shelveDTO 库房DTO
     * @return 修改数据的ID
     */
    @ApiOperation(value = "通过ID修改库房信息")
    @PutMapping(value = "/updateShelveById")
    public Integer updateShelveById(@RequestBody ShelveDTO shelveDTO) {
        return shelveService.save(shelveDTO);
    }

    /**
     * 增加库位
     *
     * @param shelveDTO 库位DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加库位")
    @PostMapping(value = "/createShelve")
    public Integer createShelve(@RequestBody ShelveDTO shelveDTO) {
        return shelveService.save(shelveDTO);
    }

    /**
     * 通过ID查询库房信息
     *
     * @param id 备件DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "通过ID查询库房信息 ")
    @GetMapping(value = "/getShelveById/{id}")
    public ShelveDTO getShelveById(@PathVariable Integer id) {
        return shelveService.findById(id);
    }

    /**
     * 删除库房
     * 当库位中有备件时不能删除
     *
     * @param id ID
     */
    @ApiOperation(value = "通过ID删除库房")
    @DeleteMapping(value = "deleteShelveById/{id}")
    public void deleteShelveById(@PathVariable Integer id) {
        shelveService.deleteById(id);
    }

    /**
     * 分页查询 通过库位编码 库位名称 库位地点 联合查询
     *
     * @param shelveQueryDTO 分页查询信息
     * @return 分页
     */
    @ApiOperation(value = "分页查询 库位 通过库位编码 库位名称 库位地点")
    @PostMapping(value = "getShelveByShelveCodeAndShelveNameAndShelvePace")
    public Page<ShelveDTO> getShelveByShelveCodeAndShelveNameAndShelvePace(@RequestBody ShelveQueryDTO shelveQueryDTO) {
        return shelveService.findAllByShelveCodeAndShelveNameAndShelvePace(shelveQueryDTO);
    }
}
