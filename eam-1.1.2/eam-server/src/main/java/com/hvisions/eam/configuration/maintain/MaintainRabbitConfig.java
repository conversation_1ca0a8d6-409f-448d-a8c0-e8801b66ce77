package com.hvisions.eam.configuration.maintain;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: TopicRabbitConfig</p>
 * <p>Description:话题模式消息队列 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Configuration
public class MaintainRabbitConfig {
    /**
     * 消息队列Queue名称
     */
    public final static String EQUIPMENT_MAINTAIN_QUEUE_NAME = "equipment-maintain";
    /**
     * timer-exchange交换机名称
     */
    private final static String TIMER_EXCHANGE_NAME = "timer-exchange";


    @Bean
    public Queue equipmentMaintain() {
        return new Queue(MaintainRabbitConfig.EQUIPMENT_MAINTAIN_QUEUE_NAME);
    }

    @Bean
    TopicExchange exchange() {
        return new TopicExchange(TIMER_EXCHANGE_NAME);
    }

    /**
     * 绑定队列和关注的消息
     *
     * @param equipmentMaintain 队列
     * @param exchange          分发器
     * @return 绑定
     */
    @Bean
    Binding bindingExchangeMessage2(Queue equipmentMaintain, TopicExchange exchange) {
        return BindingBuilder.bind(equipmentMaintain).to(exchange).with("timer.simple");
    }

}
