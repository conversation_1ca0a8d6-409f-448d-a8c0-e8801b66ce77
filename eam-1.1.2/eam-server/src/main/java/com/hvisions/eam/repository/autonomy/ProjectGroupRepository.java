package com.hvisions.eam.repository.autonomy;

import com.hvisions.eam.entity.autonomy.HvAmProjectGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectGroupRepository extends JpaRepository<HvAmProjectGroup, Integer>, JpaSpecificationExecutor<HvAmProjectGroup> {

    /**
     * 查询ParentId为null的数据
     *
     * @return 分组信息list
     */
    List<HvAmProjectGroup> findAllByParentIdIsNull();

    /**
     * 根据ParentId查询数据
     * @param parentId 父级id
     * @return 分组信息list
     */
    List<HvAmProjectGroup> findAllByParentId(Integer parentId);

    /**
     * 根据Id查询数据
     * @param parentId 主键
     * @return 项目信息
     */
    HvAmProjectGroup findAllById(Integer parentId);
}
