package com.hvisions.eam.repository.sprare;

import com.hvisions.eam.entity.spare.HvEamSpareToShelve;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title:SpareToShelveEntityRepository</p>
 * <p>Description:备件库位关系</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface SpareToShelveEntityRepository extends JpaRepository<HvEamSpareToShelve, Integer> {

    /**
     * 通过 备件id 和 库房id 查询是否有数据存在
     *
     * @param spareId  备件id
     * @param shelveId 库房id
     * @return 数据存在
     */
    boolean existsBySpareIdAndShelveId(Integer spareId, Integer shelveId);

    /**
     * 通过 备件ID 查询 库位ID
     *
     * @param spareId 备件ID
     * @return 库位ID集合
     */
    List<HvEamSpareToShelve> findShelveIdBySpareId(Integer spareId);

    /**
     * 通过 库位ID 查询 备件ID
     *
     * @param storehouseId 库位ID
     * @return 备件ID集合
     */
    List<HvEamSpareToShelve> findSpareIdByShelveId(Integer storehouseId);

    /**
     * 查询 全部 通过 备件ID
     *
     * @param spareId 备件ID
     * @return 全部
     */
    List<HvEamSpareToShelve> findAllBySpareId(Integer spareId);

    /**
     * 查询 全部 通过 库位ID
     *
     * @param storehouseId 库位Id
     * @return 全部
     */
    List<HvEamSpareToShelve> findAllByShelveId(Integer storehouseId);

    /**
     * 查询 全部信息 通过 备件ID 和 库位ID 和 批次号
     *
     * @param spareId     备件ID
     * @param shelveId    库位ID
     * @param batchNumber 批次号
     * @return 备件库位关系
     */
    HvEamSpareToShelve findAllBySpareIdAndShelveIdAndBatchNumber(Integer spareId, Integer shelveId, String batchNumber);

    /**
     * 查询 全部信息 通过 备件id 和库房id 和批次号模糊查询
     *
     * @param spareId     备件id
     * @param shelveId    库房id
     * @param batchNumber 批次号
     * @return 关系
     */
    List<HvEamSpareToShelve> findBySpareIdAndShelveIdAndBatchNumberContains(Integer spareId, Integer shelveId, String batchNumber);

    /**
     * 查询 全部信息 通过 备件id 和库房id 和批次号模糊查询
     *
     * @param spareId     备件id
     * @param batchNumber 批次号
     * @return 关系
     */
    List<HvEamSpareToShelve> findBySpareIdAndBatchNumberContains(Integer spareId,String batchNumber);

    /**
     * 通过 备件id 和 库房id 查询批次号信息
     *
     * @param spareId  备件id
     * @param shelveId 库房id
     * @return 批次号信息
     */
    List<HvEamSpareToShelve> findBySpareIdAndShelveId(Integer spareId, Integer shelveId);

}
