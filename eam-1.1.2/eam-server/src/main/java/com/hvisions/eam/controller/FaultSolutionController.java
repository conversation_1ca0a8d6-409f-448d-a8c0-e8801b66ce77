package com.hvisions.eam.controller;

import com.hvisions.eam.dto.fault.FaultSolutionDTO;
import com.hvisions.eam.dto.fault.FaultSolutionQueryDTO;
import com.hvisions.eam.service.fault.FaultSolutionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;


/**
 * <p>Title: HvEmFaultSolutionController</p >
 * <p>Description: 故障解决方案controller</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/26</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "故障解决方案controller")
@RequestMapping(value = "/EmFaultSolution")
@Slf4j
public class FaultSolutionController {

    /**
     * 解决方案service
     */
    private final FaultSolutionService faultSolutionService;

    @Autowired
    public FaultSolutionController(FaultSolutionService faultSolutionService) {
        this.faultSolutionService = faultSolutionService;
    }

    /**
     * 新增解决方案
     *
     * @param faultSolutionDTO 解决方案DTO
     * @return 新增实体id
     */
    @ApiOperation(value = "新增解决方案")
    @PostMapping(value = "/add")
    public Integer addEmFaultSolution(@RequestBody FaultSolutionDTO faultSolutionDTO) {
        return faultSolutionService.addOrUpdateFaultSolution(faultSolutionDTO);
    }

    /**
     * 根据id删除解决方案
     *
     * @param id 解决方案id
     */
    @ApiOperation(value = "根据id删除解决方案")
    @DeleteMapping(value = "/delete/{id}")
    public void deleteEmFaultSolution(@PathVariable Integer id) {
        faultSolutionService.deleteFaultSolution(id);
    }

    /**
     * 通过解决方案编码获取解决方案
     *
     * @param solutionCode 解决方案编码
     */
    @ApiOperation(value = "通过解决方案编码获取解决方案")
    @DeleteMapping(value = "/deleteEmFaultSolutionBySolutionCode/{solutionCode}")
    public void deleteEmFaultSolutionBySolutionCode(@PathVariable String solutionCode) {
        faultSolutionService.deleteFaultSolutionByCode(solutionCode);
    }

    /**
     * 更新解决方案
     *
     * @param faultSolutionDTO 解决方案DTO
     */
    @ApiOperation(value = "更新解决方案")
    @PutMapping(value = "update")
    public void updateEmFaultSolution(@RequestBody FaultSolutionDTO faultSolutionDTO) {
        faultSolutionService.addOrUpdateFaultSolution(faultSolutionDTO);
    }

    /**
     * 根据故障原因id查询解决方案
     *
     * @param faultSolutionQueryDTO 故障原因查询解决方案DTO
     * @return 解决方案集合
     */
    @ApiOperation(value = "根据故障原因id查询解决方案")
    @PostMapping(value = "getSolutionByFaultId")
    public Page<FaultSolutionDTO> getSolutionsByFaultId(@RequestBody FaultSolutionQueryDTO faultSolutionQueryDTO) {
        return faultSolutionService.getFaultSolutionByFaultReasonId(faultSolutionQueryDTO);
    }


    /**
     * 根据id查询故障解决方案
     *
     * @param id 故障解决方案id
     * @return 故障解决方案
     */
    @ApiOperation(value = "根据id查询故障解决方案")
    @GetMapping(value = "getFaultSolutionById/{id}")
    public FaultSolutionDTO getFaultSolutionById(@PathVariable Integer id) {
        return faultSolutionService.getFaultSolutionById(id);
    }

    /**
     * 通过解决方案编码获取解决方案
     *
     * @param solutionCode 解决方案名称
     * @return 解决方案
     */
    @ApiOperation(value = "通过解决方案编码获取解决方案")
    @GetMapping(value = "/getFaultSolutionBySolutionCode/{solutionCode}")
    public FaultSolutionDTO getFaultSolutionBySolutionCode(@PathVariable String solutionCode) {
        return faultSolutionService.getFaultSolutionBySolutionCode(solutionCode);
    }
}