package com.hvisions.eam.configuration.autonomy;

import com.google.common.collect.Maps;
import com.hvisions.activiti.client.ActivitiClient;
import com.hvisions.activiti.dto.instance.ProcessInstanceStartDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.eam.client.autonomy.ActivitiIdentityClient;
import com.hvisions.eam.client.autonomy.AuthClient;
import com.hvisions.eam.dto.autonomy.Data;
import com.hvisions.eam.dto.autonomy.InspectionPlanDTO;
import com.hvisions.eam.dto.autonomy.InspectionProjectDTO;
import com.hvisions.eam.dto.autonomy.JsonByUserId;
import com.hvisions.eam.dto.autonomy.activitiIdentity.Content;
import com.hvisions.eam.dto.autonomy.activitiIdentity.JsonGetGroup;
import com.hvisions.eam.dto.autonomy.activitiIdentity.QueryDTO;
import com.hvisions.eam.entity.autonomy.HvAmInspectionPlan;
import com.hvisions.eam.repository.autonomy.InspectionPlanRepository;
import com.hvisions.eam.repository.autonomy.ProjectGroupRepository;
import com.hvisions.eam.service.autonomy.InspectionPlanService;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.api.RabbitListenerErrorHandler;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: TopicReceiver</p>
 * <p>Description: TopicReceiver</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
@Slf4j
public class AutonomyMaintenanceTopicReceiver {

    private final ProjectGroupRepository projectGroupRepository;
    private final InspectionPlanRepository inspectionPlanRepository;
    private final InspectionPlanService inspectionPlanService;
    private final ActivitiClient activitiClient;
    private final AuthClient authClient;
    private final ActivitiIdentityClient activitiIdentityClient;

    @Autowired
    public AutonomyMaintenanceTopicReceiver(ProjectGroupRepository projectGroupRepository, InspectionPlanRepository inspectionPlanRepository, InspectionPlanService inspectionPlanService, ActivitiClient activitiClient, AuthClient authClient, ActivitiIdentityClient activitiIdentityClient) {
        this.projectGroupRepository = projectGroupRepository;
        this.inspectionPlanRepository = inspectionPlanRepository;
        this.inspectionPlanService = inspectionPlanService;
        this.activitiClient = activitiClient;
        this.authClient = authClient;
        this.activitiIdentityClient = activitiIdentityClient;
    }

    @Bean
    public RabbitListenerErrorHandler autonomymaintenanceErrorHandler() {
        return new RabbitListenerErrorHandler() {
            @Override
            public Object handleError(Message message, org.springframework.messaging.Message<?> message1, ListenerExecutionFailedException e) throws Exception {
                log.info("autonomymaintenance" + message.toString());
                return null;
            }
        };
    }

    /**
     * 监听队列
     *
     * @param id 传入参数
     */
    @RabbitListener(queues = AutonomyRabbitConfig.AUTONOMY_MAINTENANCE_QUEUE_NAME, errorHandler = "autonomymaintenanceErrorHandler")
    public void process1(Integer id) {

        log.info("自主维护监听参数" + id);
        //根据timerId找 计划
        log.info("据timerId找到 计划，准备生成任务");
        List<HvAmInspectionPlan> all = inspectionPlanRepository.findAllByTimerId(id);

        //循环调用activitiClient生成流程
        for (HvAmInspectionPlan hvAmInspectionPlan : all) {
            InspectionPlanDTO plan = DtoMapper.convert(hvAmInspectionPlan,InspectionPlanDTO.class);

            //通过 计划ID 查询出对应的 项目详细信息
            List<InspectionProjectDTO> temp = inspectionPlanService.getProjectList(plan.getId());
            List<InspectionProjectDTO> projectDTOList = new ArrayList<>();
            //排除已经停用的项目 并查询组ID
            for (InspectionProjectDTO inspectionProjectDTO:temp) {
                if ("0".equals(inspectionProjectDTO.getEnableFlag())){
                    inspectionProjectDTO.setGroupName(projectGroupRepository.findAllById(inspectionProjectDTO.getGroupId()).getName());
                    projectDTOList.add(inspectionProjectDTO);
                }
            }

            //一个计划生成一条任务
            ProcessInstanceStartDTO processInstanceStartDTO = new ProcessInstanceStartDTO();
            Map<String, Object> map = Maps.newHashMap();
            String businessKey = "autonomymaintenanceErrorHandler-" + plan.getId() + "-" + Calendar.getInstance().getTimeInMillis();
            processInstanceStartDTO.setBusinessKey(businessKey);
            processInstanceStartDTO.setProcessDefinitionKey("autonomymaintenance");

            //设置任务的各种参数
            map.put("number", plan.getNumber());//检查计划编码
            map.put("taskName", plan.getName());//检查计划名称
            map.put("typesOfLiability",plan.getTypesOfLiability());//责任类型;0-责任人,1-责任组
            if ("0".equals(plan.getTypesOfLiability())){
                map.put("personLiable", plan.getPersonLiable());//责任人
                try{
                    JsonByUserId jsonByUserId = authClient.getUserById(Integer.parseInt(plan.getPersonLiable()));
                    Data data = jsonByUserId.getData();
                    map.put("personLiableName", data.getUserName());//责任人-名称
                }catch (Exception e){
                    map.put("personLiableName", plan.getPersonLiable());//责任人-名称 获取失败 展示id
                }
                map.put("responsibilityGroup", null);//责任组
                map.put("responsibilityGroupName", null);//责任组-名称
            }else {
                map.put("personLiable", null);//责任人
                map.put("personLiableName", null);//责任人-名称
                map.put("responsibilityGroup", plan.getResponsibilityGroup());//责任组
                try{
                    QueryDTO queryDTO = new QueryDTO();
                    queryDTO.setId(plan.getResponsibilityGroup());
                    queryDTO.setSortCol("id");
                    JsonGetGroup groupByQuery = activitiIdentityClient.getGroupByQuery(queryDTO);
                    List<Content> content = groupByQuery.getData().getContent();
                    map.put("responsibilityGroupName", content.get(0).getName());//责任组-名称
                }catch (Exception e){
                    map.put("responsibilityGroupName", plan.getResponsibilityGroup());//responsibilityGroupName 获取失败 展示id
                }
            }
            map.put("verifier", plan.getVerifier());//验证人员
            try{
                JsonByUserId jsonByUserId = authClient.getUserById(Integer.parseInt(plan.getVerifier()));
                Data data = jsonByUserId.getData();
                map.put("verifierName", data.getUserName());//验证人员-名称
            }catch (Exception e){
                map.put("verifierName", plan.getVerifier());//验证人员-名称 获取失败 展示id
            }
            map.put("foreman", plan.getForeman());//班组长
            try{
                JsonByUserId jsonByUserId = authClient.getUserById(Integer.parseInt(plan.getForeman()));
                Data data = jsonByUserId.getData();
                map.put("foremanName", data.getUserName());//班组长-名称
            }catch (Exception e){
                map.put("foremanName", plan.getForeman());//班组长-名称 获取失败 展示id
            }
            map.put("leader", plan.getLeader());//主管领导
            try{
                JsonByUserId jsonByUserId = authClient.getUserById(Integer.parseInt(plan.getLeader()));
                Data data = jsonByUserId.getData();
                map.put("leaderName", data.getUserName());//主管领导-名称
            }catch (Exception e){
                map.put("leaderName", plan.getLeader());//主管领导-名称 获取失败 展示id
            }
            map.put("descr",plan.getName()+"验证人员："+map.get("verifierName")+",班组长："+map.get("foremanName"));
            map.put("timerId",plan.getTimerId());//timerId
            map.put("projectDTOList", projectDTOList);

            processInstanceStartDTO.setVariables(map);

            ResultVO<ProcessInstanceDTO> processInstanceDTOResultVO = new ResultVO<>();
            try {
                processInstanceDTOResultVO = activitiClient.startProcessInstance(processInstanceStartDTO);
                if (processInstanceDTOResultVO.isSuccess()){
                    String processInstanceId = processInstanceDTOResultVO.getData().getProcessInstanceId();
                    log.info("启动流程实例成功，流程实例ID为：" + processInstanceId);
                }
            }catch (Exception e){
                log.error("启动流程实例失败,异常信息:{} , 入参为：{} ", processInstanceDTOResultVO.getMessage(),processInstanceStartDTO.toString());
            }
        }
    }

}
