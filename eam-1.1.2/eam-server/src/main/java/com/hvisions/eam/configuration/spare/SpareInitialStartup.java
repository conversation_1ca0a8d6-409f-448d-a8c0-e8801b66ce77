package com.hvisions.eam.configuration.spare;

import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.eam.service.publicstore.PublicStoreCheckBpmnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <p>Title:InitialStartup</p>
 * <p>Description:程序初始启动，检查acticiti流程检查</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/11</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Component
public class SpareInitialStartup extends SafetyCommandLineRunner {

    @Autowired
    PublicStoreCheckBpmnService checkActivitiBpmnService;

    /**
     * 是否开启审批
     */
    @Value("${h-visions.approve}")
    private Boolean isApproval;

    @Override
    public void callRunner(String... args) {
        if (isApproval) {
            log.info(" ---= 开启审批 =--- ");
            try {
                checkActivitiBpmnService.checkActivitiBpmn();
            } catch (Exception e) {
                log.info(e.getMessage());
                log.info(" ---= activiti初始化失败 =--- ");
            }

        } else {
            log.info(" ---= 关闭审批 =--- ");
        }
        // 备件扫码解析规则初始化
        try {
            checkActivitiBpmnService.matching();
        } catch (Exception ex) {
            log.error("备件扫描解析规则初始化出错");
            log.error(ex.getMessage());
        }
    }
}
