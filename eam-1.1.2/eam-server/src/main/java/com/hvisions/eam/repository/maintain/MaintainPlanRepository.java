package com.hvisions.eam.repository.maintain;

import com.hvisions.eam.entity.maintain.HvEamMaintainPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: MaintainPlanRepository</p >
 * <p>Description: 保养计划Repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/19</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaintainPlanRepository extends JpaRepository<HvEamMaintainPlan, Integer> {


    /**
     * 根据timerId获取所有已经启用的保养计划
     *
     * @param id           timerId
     * @param conditionIds 审批状态idList
     * @return 保养计划List
     */
    List<HvEamMaintainPlan> findAllByTimerIdAndStartUsingIsTrueAndMaintainPlanConditionIdIn(Integer id, List<Integer> conditionIds);


    /**
     * 根据timerId查询
     *
     * @param timerId timerId
     * @return 计划集合
     */
    List<HvEamMaintainPlan> findAllByTimerId(Integer timerId);


    /**
     * 根据timerid列表查询保养计划
     *
     * @param timerIds    时间
     * @param conditionId 保养计划状态
     * @return 保养计划列表
     */
    List<HvEamMaintainPlan> findAllByTimerIdInAndMaintainPlanConditionIdIn(List<Integer> timerIds, List<Integer> conditionId);

}
