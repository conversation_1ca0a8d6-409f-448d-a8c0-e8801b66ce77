package com.hvisions.eam.repository.autonomy;

import com.hvisions.eam.entity.autonomy.HvAmInspectionPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InspectionPlanRepository extends JpaRepository<HvAmInspectionPlan, Integer>, JpaSpecificationExecutor<HvAmInspectionPlan> {

    /**
     * 验证计划编码是否存在
     *
     * @param number 计划编码
     * @return 是否
     */
    boolean existsByNumber(String number);

    /**
     * 根据timerId获取所有计划
     * <AUTHOR>
     * @param timerId 计划id
     * @return 计划list
     */
    List<HvAmInspectionPlan> findAllByTimerId(Integer timerId);

}
