package com.hvisions.eam.controller;

import com.hvisions.eam.dao.inspect.InspectReportStatisticalMapper;
import com.hvisions.eam.dto.inspect.table.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title: InspectStatisticalController</p >
 * <p>Description: 巡检项目任务统计接口</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/8</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/inspectProcessStatistical")
@Api(description = "巡检项目任务统计接口")
public class InspectProcessStatisticalController {
    private final InspectReportStatisticalMapper reportStatisticalMapper;

    @Autowired
    public InspectProcessStatisticalController(
            InspectReportStatisticalMapper reportStatisticalMapper) {
        this.reportStatisticalMapper = reportStatisticalMapper;
    }

    /**
     * 获取巡检项数据报表
     *
     * @param inspectProcessQueryDTO 查询条件
     * @return 历史
     */
    @ApiOperation(value = "获取巡检项数据报表")
    @PostMapping(value = "/queryForInspectProcessTable")
    public List<InspectProcessResultDTO> queryForInspectProcessTable(@RequestBody InspectProcessQueryDTO inspectProcessQueryDTO) {
        return reportStatisticalMapper.queryForInspectProcessTable(inspectProcessQueryDTO);
    }

    /**
     * 获取巡检任务数据报表
     *
     * @param inspectTaskQueryDTO 查询条件
     * @return 历史
     */
    @ApiOperation(value = "获取巡检任务数据报表")
    @PostMapping(value = "/queryForInspectTaskTable")
    public List<InspectTaskResultDTO> queryForInspectTaskTable(@RequestBody InspectTaskQueryDTO inspectTaskQueryDTO) {
        return reportStatisticalMapper.queryForInspectTaskTable(inspectTaskQueryDTO);
    }

    /**
     * 每月巡检项总数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @Cacheable(value = "countForAllItem", key = "T(String).valueOf(#monthQueryDTO.yearTime).concat('-').concat(T(String).valueOf(#monthQueryDTO.monthTime))")
    @ApiOperation(value = "每月巡检项总数")
    @PostMapping(value = "/countForAllItem")
    public Integer countForAllItem(@RequestBody MonthQueryDTO monthQueryDTO) {
        log.info("测试");
        return reportStatisticalMapper.countForAllItem(monthQueryDTO);
    }

    /**
     * 每月合格巡检项总数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @Cacheable(value = "countForQualifiedItem", key = "T(String).valueOf(#monthQueryDTO.yearTime).concat('-').concat(T(String).valueOf(#monthQueryDTO.monthTime))")
    @ApiOperation(value = "每月合格巡检项总数")
    @PostMapping(value = "/countForQualifiedItem")
    public Integer countForQualifiedItem(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countForQualifiedItem(monthQueryDTO);
    }

    /**
     * 每月不合格巡检项总数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @Cacheable(value = "countForUnqualifiedItem", key = "T(String).valueOf(#monthQueryDTO.yearTime).concat('-').concat(T(String).valueOf(#monthQueryDTO.monthTime))")
    @ApiOperation(value = "每月不合格巡检项总数")
    @PostMapping(value = "/countForUnqualifiedItem")
    public Integer countForUnqualifiedItem(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countForUnqualifiedItem(monthQueryDTO);
    }

    /**
     * 每天巡检项总数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @ApiOperation(value = "每天巡检项总数")
    @PostMapping(value = "/countForDailyItem")
    public List<ItemStatisticalDTO> countForDailyItem(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countForDailyItem(monthQueryDTO);
    }

    /**
     * 设备不合格数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @ApiOperation(value = "设备不合格数")
    @PostMapping(value = "/countForUnqualifiedItemGroupByEquipment")
    public List<ItemStatisticalDTO> countForUnqualifiedItemGroupByEquipment(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countForUnqualifiedItemGroupByEquipment(monthQueryDTO);
    }

    /**
     * 巡检项不合格数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @ApiOperation(value = "巡检项不合格数")
    @PostMapping(value = "/countForUnqualifiedItemGroupByItem")
    public List<ItemStatisticalDTO> countForUnqualifiedItemGroupByItem(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countForUnqualifiedItemGroupByItem(monthQueryDTO);
    }

    /**
     * 巡检项不合格数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @ApiOperation(value = "巡检项不合格数")
    @PostMapping(value = "/countForUnqualifiedItemGroupByItemAndEquipment")
    public List<ItemStatisticalDTO> countForUnqualifiedItemGroupByItemAndEquipment(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countForUnqualifiedItemGroupByItemAndEquipment(monthQueryDTO);
    }

    /**
     * 巡检任务数据
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @ApiOperation(value = "巡检任务数据")
    @PostMapping(value = "/countForTask")
    public TaskStatisticalDTO countForTask(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countForTask(monthQueryDTO);
    }

    /**
     * 人员平均完成时间
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @ApiOperation(value = "人员平均完成时间")
    @PostMapping(value = "/countDurationByPerson")
    public List<InspectTaskStaDTO> countDurationByPerson(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countDurationByPerson(monthQueryDTO);
    }

    /**
     * 人员完成任务数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @ApiOperation(value = "人员完成任务数")
    @PostMapping(value = "/countTaskNumByPerson")
    public List<InspectTaskStaDTO> countTaskNumByPerson(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countTaskNumByPerson(monthQueryDTO);
    }

    /**
     * 每天巡检任务数量
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @ApiOperation(value = "每天巡检任务数量")
    @PostMapping(value = "/countTaskNumByPersonAndTaskName")
    public List<InspectTaskStaDTO> countTaskNumByPersonAndTaskName(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countTaskNumByPersonAndTaskName(monthQueryDTO);
    }

    /**
     * 每天巡检任务时间
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @ApiOperation(value = "每天巡检任务时间")
    @PostMapping(value = "/countDurationByPersonAndTaskName")
    public List<InspectTaskStaDTO> countDurationByPersonAndTaskName(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countDurationByPersonAndTaskName(monthQueryDTO);
    }

    /**
     * 人员任务延迟时间（平均）
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    @ApiOperation(value = "人员任务延迟时间")
    @PostMapping(value = "/countDelayByPerson")
    public List<InspectTaskStaDTO> countDelayByPerson(@RequestBody MonthQueryDTO monthQueryDTO) {
        return reportStatisticalMapper.countDelayByPerson(monthQueryDTO);
    }


}