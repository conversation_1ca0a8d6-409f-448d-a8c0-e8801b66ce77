package com.hvisions.eam.configuration.inspect;

import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.eam.service.inspect.InspectCheckActivitiBpmnService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>Title: InitialStartup</p >
 * <p>Description: 程序初始启动，检查acticiti是否有对应的流程文件,若无则需发布</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class InspectInitialStartup extends SafetyCommandLineRunner {

    @Autowired
    InspectCheckActivitiBpmnService checkActivitiBpmnService;

    @Override
    public void callRunner(String... args) throws Exception {
        checkActivitiBpmnService.checkActivitiBpmn();
    }
}