package com.hvisions.eam.controller;

import com.hvisions.activiti.client.HistoryClient;
import com.hvisions.activiti.dto.history.HistoricProcessQuery;
import com.hvisions.activiti.dto.history.HistoryProcessInstanceDTO;
import com.hvisions.activiti.dto.instance.ProcessInstanceQueryDTO;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dao.inspect.InspectReportMapper;
import com.hvisions.eam.dto.inspect.report.BoardDTO;
import com.hvisions.eam.dto.inspect.report.ItemFinishFail;
import com.hvisions.eam.dto.inspect.report.PassRate;
import com.hvisions.eam.dto.inspect.report.ProcessInfoDTO;
import com.hvisions.eam.enums.ProcessFinishTypeEnum;
import com.hvisions.eam.dao.inspect.BoardMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * <p>Title: InspectReportController</p>
 * <p>Description: 报表控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/1/14</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "报表控制器-看板")
@RequestMapping("/inspectReport")
public class InspectReportController {
    private final BoardMapper boardMapper;
    private final InspectReportMapper inspectReportMapper;
    private final HistoryClient historyClient;

    @Autowired
    public InspectReportController(BoardMapper boardMapper, InspectReportMapper inspectReportMapper, HistoryClient historyClient) {
        this.boardMapper = boardMapper;
        this.inspectReportMapper = inspectReportMapper;
        this.historyClient = historyClient;
    }

    @PostMapping("/getProcessInstance")
    @ApiOperation("获取设备的流程实例信息")
    @ApiResultIgnore
    public ResultVO<HvPage<HistoryProcessInstanceDTO>> getProcessInstance(@RequestBody HistoricProcessQuery query) {
        Object equipmentId = Optional.ofNullable(query)
            .map(ProcessInstanceQueryDTO::getVariableValueEquals)
            .map(t -> t.get("equipmentId"))
            .orElseThrow(() -> new BaseKnownException(10000, "需要传递设备id"));
        Assert.isTrue(equipmentId instanceof Integer, "需要传递设备id");
        List<String> processInstanceIds = inspectReportMapper.getProcessInstanceIds((Integer) equipmentId);
        if (CollectionUtils.isEmpty(processInstanceIds)) {
            //流程id给一个不存在的。查一个空白的列表
            processInstanceIds.add("-1");
        }
        query.setProcessInstanceIds(processInstanceIds);
        query.getVariableValueEquals().remove("equipmentId");
        return historyClient.getProcessInstance(query);

    }

    /**
     * 获取看板数据
     *
     * @return 看板数据
     */
    @GetMapping("/getBoard")
    @ApiOperation("获取看板数据")
    public BoardDTO getBoardInfo() {
        BoardDTO result = new BoardDTO();
        LocalDate now = LocalDate.now();
        //获取周一的0点时间
        LocalDate monday = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        //查询点巡检任务数量
        result.setTotalInspectNumber(boardMapper.getTaskCount(LocalDate.MIN));
        result.setTodayInspectNumber(boardMapper.getTaskCount(now));
        result.setMonthInspectNumber(boardMapper.getTaskCount(now.withDayOfMonth(1)));
        result.setYearInspectNumber(boardMapper.getTaskCount(now.withDayOfYear(1)));
        //查询点巡检项目数量
        result.setTotalInspectItemNumber(boardMapper.getItemCount(LocalDate.MIN));
        result.setTodayInspectItemNumber(boardMapper.getItemCount(now));
        result.setMonthInspectItemNumber(boardMapper.getItemCount(now.withDayOfMonth(1)));
        result.setYearInspectItemNumber(boardMapper.getItemCount(now.withDayOfYear(1)));
        //查询当天的点巡检任务数量
        result.setTodayFinishedInspectItemNumber(boardMapper.getFinishedItemCount(now));
        //查询当天最近一次的点巡检时间
        result.setLastInspectFinishTime(boardMapper.getLastInspectTime());
        //查询本周以来的点巡检流程实例信息,以及各个任务数量的饼图信息
        List<ProcessInfoDTO> dayInfo = inspectReportMapper.getInspectDayInfo(monday);
        Integer[][] weekInspectInfo = new Integer[7][24];
        Map<Integer, Integer> map = new HashMap<>();
        map.put(ProcessFinishTypeEnum.FINISH.getCode(), 0);
        map.put(ProcessFinishTypeEnum.DELAY.getCode(), 0);
        map.put(ProcessFinishTypeEnum.MISS.getCode(), 0);
        //遍历所有的任务信息，如果他的时间没有数据。则直接录入任务的数据，如果已经有了某个任务数据。那么值钱的数据和当前数据比较。取较大的值。
        //有遗漏显示遗漏，有延迟显示延迟，
        for (ProcessInfoDTO dto : dayInfo) {
            Integer value = weekInspectInfo[dto.dayOfWeek()][dto.hourOfDay()];
            if (value == null) {
                weekInspectInfo[dto.dayOfWeek()][dto.hourOfDay()] = dto.getFinishType().getCode();
            } else {
                if (value < dto.getFinishType().getCode()) {
                    weekInspectInfo[dto.dayOfWeek()][dto.hourOfDay()] = dto.getFinishType().getCode();
                }
            }
            //如果不是没有任务，那么添加到分组数据当中
            if (dto.getFinishType() != ProcessFinishTypeEnum.NO_TASK) {
                map.put(dto.getFinishType().getCode(), map.get(dto.getFinishType().getCode()) + 1);
            }
        }
        result.setWeekInspectInfo(weekInspectInfo);
        result.setTaskFinishInfo(map);
        //获取最近一个月巡检项目合格情况
        List<PassRate> passRates = new ArrayList<>();
        List<ItemFinishFail> finishFails = inspectReportMapper.getPassRate(now.withDayOfMonth(1));
        //显示月初到当前的数据
        for (int i = 1; i <= now.getDayOfMonth(); i++) {
            PassRate passRate = new PassRate();
            passRate.setDate(now.withDayOfMonth(i));
            int finalI = i;
            long passCount = finishFails.stream()
                .filter(
                    t -> t.getFinishTime().compareTo(now.withDayOfMonth(finalI).atStartOfDay()) > 0
                        && t.getFinishTime().compareTo(now.withDayOfMonth(finalI + 1).atStartOfDay()) <= 0
                        && t.getFail() == 2

                ).count();
            long failCount = finishFails.stream()
                .filter(
                    t -> t.getFinishTime().compareTo(now.withDayOfMonth(finalI).atStartOfDay()) > 0
                        && t.getFinishTime().compareTo(now.withDayOfMonth(finalI + 1).atStartOfDay()) <= 0
                        && t.getFail() == 1

                ).count();
            passRate.setFailNumber((int) failCount);
            passRate.setPassNumber((int) passCount);
            passRates.add(passRate);
        }
        result.setPassRateList(passRates);
        //获取Top10 巡检项信息
        result.setFailItemInfoList(inspectReportMapper.getTop10Items(now.withDayOfMonth(1)));
        //获取Top10 巡检人员工作量
        result.setUserInspectItem(inspectReportMapper.getTop10Worker(now.withDayOfMonth(1)));
        return result;
    }
}









