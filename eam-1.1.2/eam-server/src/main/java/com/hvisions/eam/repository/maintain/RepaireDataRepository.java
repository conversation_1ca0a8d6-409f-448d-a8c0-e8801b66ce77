package com.hvisions.eam.repository.maintain;

import com.hvisions.eam.entity.maintain.HvEamRepairData;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <p>Title: RepaireDataRepository</p>
 * <p>Description: 维修数据存储</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/11/6</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface RepaireDataRepository extends JpaRepository<HvEamRepairData,Integer> {
    /**
     * 查询是否已经存在数据
     *
     * @param id 流程实例id
     * @return 流程实例id
     */
    boolean existsByProcessInstanceId(String id);

}

    
    
    
    