package com.hvisions.eam.repository.maintain;

import com.hvisions.eam.entity.maintain.HvEamMaintainItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: MaintainItemRepository</p >
 * <p>Description: 保养项目Repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaintainItemRepository extends JpaRepository<HvEamMaintainItem, Integer> {
    /**
     * 验证保养项目code是否存在
     *
     * @param maintainItemCode 保养项目编码
     * @return 是否
     */
    boolean existsByMaintainItemCode(String maintainItemCode);

    /**
     * 根据保养项目code查询项目
     *
     * @param maintainItemCode 保养项目code
     * @return 保养项目实体
     */
    HvEamMaintainItem findByMaintainItemCode(String maintainItemCode);

    /**
     * 获取所有周期
     *
     * @return 周期集合
     */
    @Query(nativeQuery = true, value = "select distinct cycle from hv_eam_maintain_item where cycle is not null and cycle != ''")
    List<String> getAllCycles();
}
