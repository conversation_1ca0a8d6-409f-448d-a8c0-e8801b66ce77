package com.hvisions.eam.configuration.maintain;

import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.eam.dao.DataMapper;
import com.hvisions.eam.repository.maintain.RepaireDataRepository;
import com.hvisions.eam.service.maintain.RepairDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: InitialStartup</p >
 * <p>Description:用于同步所有工作流中没有同步的设备保养数据
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class RepairDataSyncStartUp extends SafetyCommandLineRunner {

    @Autowired
    RepairDataService repairDataService;

    @Autowired
    RepaireDataRepository repository;

    @Autowired
    DataMapper dataMapper;

    @Override
    public void callRunner(String... args) {
        List<String> processInstanceIds = dataMapper.getRepairProcessIds();
        if (processInstanceIds != null) {
            for (String processInstanceId : processInstanceIds) {
                repairDataService.save(processInstanceId);
            }
        }
    }
}