package com.hvisions.eam.configuration.inspect;

import com.hvisions.eam.enums.PlanConditionEnum;
import com.hvisions.eam.entity.inspect.HvEamInspectPlan;
import com.hvisions.eam.repository.inspect.InspectPlanRepository;
import com.hvisions.eam.service.inspect.InspectPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.api.RabbitListenerErrorHandler;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: TopicReceiver</p>
 * <p>Description: TopicReceiver</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
@Slf4j
public class InspectTopicReceiver {
    private final InspectPlanRepository inspectPlanRepository;
    private final InspectPlanService inspectPlanService;

    /**
     * 计划是否需要审批
     */
    @Value("${h-visions.approve}")
    private Boolean approve;

    @Autowired
    public InspectTopicReceiver(InspectPlanRepository inspectPlanRepository,
                                InspectPlanService inspectPlanService) {
        this.inspectPlanRepository = inspectPlanRepository;

        this.inspectPlanService = inspectPlanService;
    }

    @Bean
    public RabbitListenerErrorHandler inspectErrorHandler() {
        return new RabbitListenerErrorHandler() {
            @Override
            public Object handleError(Message message, org.springframework.messaging.Message<?> message1, ListenerExecutionFailedException e) throws Exception {
                log.info("inspect" + message.toString());
                return null;
            }
        };
    }

    /**
     * 监听队列
     *
     * @param id 传入参数
     */
    @RabbitListener(queues = InspectTopicRabbitConfig.EQUIPMENT_INSPECT_QUEUE_NAME, errorHandler = "inspectErrorHandler")
    public void process1(Integer id) {
        log.info("监听到队列,TimerID:{}", id);
        List<Integer> conditionIds = new ArrayList<>();
        conditionIds.add(PlanConditionEnum.PASSED.getCode());
        conditionIds.add(PlanConditionEnum.NO_APPROVE.getCode());
        //根据timerId找已经启用的计划状态为审批通过或者无需审批的计划
        log.info("根据timerId获取已经启用的并且是审批通过或者无需审批的点巡检计划，准备生成任务");
        List<HvEamInspectPlan> all = inspectPlanRepository.findAllByTimerIdAndStartUsingIsTrueAndAndInspectPlanConditionIdIn(id, conditionIds);
        log.info("找到了合适的计划数量:{}", all.size());
        //循环调用activitiClient生成流程
        for (HvEamInspectPlan hvEamPlan : all) {
            String processInstanceId = inspectPlanService.triggerPlanById(hvEamPlan.getId());
            log.info("创建了流程示例:{}", processInstanceId);
        }
    }


}









