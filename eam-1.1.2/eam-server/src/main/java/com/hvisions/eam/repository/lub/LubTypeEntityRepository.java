package com.hvisions.eam.repository.lub;

import com.hvisions.eam.entity.lub.HvEamLubType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title:SpareTypeEntityRepository</p>
 * <p>Description:油品类型</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface LubTypeEntityRepository extends JpaRepository<HvEamLubType, Integer> {

    /**
     * 通过父级ID查询 子级全部信息
     *
     * @param parentId 父级ID
     * @return 子级全部信息
     */
    List<HvEamLubType> findAllByParentId(Integer parentId);

    /**
     * 通过类型名称查询类型
     *
     * @param typeName 类型名称
     * @return 油品的类型
     */
    HvEamLubType findByTypeName(String typeName);
}


