package com.hvisions.eam.configuration.maintain;

import com.hvisions.eam.dao.DataMapper;
import com.hvisions.eam.repository.maintain.MaintainProcessDataRepository;
import com.hvisions.eam.service.maintain.MaintainDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: InitialStartup</p >
 * <p>Description:用于同步所有工作流中没有同步的设备保养数据
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
@Slf4j
public class MaintainDataSyncStartUp implements CommandLineRunner {

    @Autowired
    MaintainDataService maintainDataService;
    @Autowired
    MaintainProcessDataRepository repository;
    @Autowired
    JdbcTemplate jdbcTemplate;
    @Autowired
    DataMapper dataMapper;


    @Override
    public void run(String... args) {
        try {
            List<String> processlist = dataMapper.getMaintainProcessIds();
            for (String process : processlist) {
                maintainDataService.save(process);
            }
        } catch (Exception ex) {
            log.error("同步数据异常", ex);
        }
    }
}