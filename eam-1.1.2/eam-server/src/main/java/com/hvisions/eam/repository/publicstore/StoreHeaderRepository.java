package com.hvisions.eam.repository.publicstore;

import com.hvisions.eam.entity.publicstore.HvEamStoreHeader;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title:HeaderRepository</p>
 * <p>Description:头表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/31</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface StoreHeaderRepository extends JpaRepository<HvEamStoreHeader, Integer> {

    Page<HvEamStoreHeader> findAll(Specification<HvEamStoreHeader> specification, Pageable pageInfo);

    /**
     * 通过processInstance 和 类型 获取 出库单 出库入库1入 2出
     * @param processInstance pro
     * @param typeClass type
     * @param inOut inout
     * @return 出库单
     */
    List<HvEamStoreHeader> findByProcessInstanceIdAndTypeClassAndInOut(String processInstance,Integer typeClass,Integer inOut);

}
