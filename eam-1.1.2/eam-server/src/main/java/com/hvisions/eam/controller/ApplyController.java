package com.hvisions.eam.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.lub.RootLubDTO;
import com.hvisions.eam.activiti.sapre.ImportSpareDTO;
import com.hvisions.eam.activiti.sapre.RootSpareDTO;
import com.hvisions.eam.dto.publicstore.HeaderDTO;
import com.hvisions.eam.dto.publicstore.LineDTO;
import com.hvisions.eam.dto.publicstore.RejectDTO;
import com.hvisions.eam.query.publicstore.HeaderQueryDTO;
import com.hvisions.eam.repository.publicstore.ShelveEntityRepository;
import com.hvisions.eam.service.publicstore.ApplyService;
import com.hvisions.eam.service.publicstore.StoreHeaderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title:ApplyController</p>
 * <p>Description:备件流动记录申请记录</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@RequestMapping(value = "/apply")
@Api(description = "备件流动记录")
@Slf4j
public class ApplyController {

    /**
     * 备件审批记录
     */
    private final ApplyService applyService;

    /**
     * 头表jpa
     */
    private final StoreHeaderService storeHeaderService;

    /**
     * 库房jpa
     */
    private final ShelveEntityRepository shelveEntityRepository;

    @Autowired
    public ApplyController(ApplyService applyService, StoreHeaderService storeHeaderService, ShelveEntityRepository shelveEntityRepository) {
        this.applyService = applyService;
        this.storeHeaderService = storeHeaderService;
        this.shelveEntityRepository = shelveEntityRepository;
    }

    //----------------------------------= 入库申请 =---------------------------------------

    /**
     * 发起备件出/入库申请
     *
     * @param rootSpareDTO 出库申请申请单
     * @return 出库申请单
     */
    @ApiOperation(value = "发起备件出/入库申请")
    @PostMapping(value = "/applySpare")
    public RootSpareDTO applySpare(@RequestBody @Valid RootSpareDTO rootSpareDTO) {
        return applyService.applySpare(rootSpareDTO);
    }

    /**
     * 发起油品出/入库申请
     *
     * @param rootLubDTO 申请单
     * @return 申请单
     */
    @ApiOperation(value = "发起油品出/入库申请")
    @PostMapping(value = "/applyLub")
    public RootLubDTO applyLub(@RequestBody @Valid RootLubDTO rootLubDTO) {
        return applyService.applyLub(rootLubDTO);
    }

    /**
     * 导入备件入库申请信息信息
     *
     * @param file 备件入库申请信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importInSpare")
    @ApiOperation(value = "导入备件入库申请信息信息")
    public ImportResult importInSpare(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return applyService.importInSpare(file);
    }


    /**
     * 导入备件出库库申请信息信息
     *
     * @param file 备件出库申请信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importOutSpare")
    @ApiOperation(value = "导入备件出库库申请信息信息")
    public ImportResult importOutSpare(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return applyService.importOutSpare(file);
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @EnableFilter
    @ApiResultIgnore
    @GetMapping(value = "/getSpareImportTemplateLink")
    @ApiOperation(value = "获取导入模板,支持超链接")
    public ResponseEntity<byte[]> getSpareImportTemplateLink() throws IOException, IllegalAccessException {
        return ExcelUtil.generateImportFile(ImportSpareDTO.class, "spareInOut.xls");
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @EnableFilter
    @ApiResultIgnore
    @GetMapping(value = "/getSpareImportTemplate")
    @ApiOperation(value = "获取备件导入模板")
    public ResultVO<ExcelExportDto> getSpareImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result =
                ExcelUtil.generateImportFile(ImportSpareDTO.class,
                        "spareInOut.xls");
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName("spareInOut.xls");
        return ResultVO.success(excelExportDto);
    }

    //----------------------------------= 审批 =------------------------------------------

    /**
     * 备件出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3
     *
     * @param taskId 任务ID
     * @param isPass 是否通过
     */
    @Deprecated
    @ApiOperation(value = "备件 油品出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3")
    @GetMapping(value = "/spareAndLubIsThrough/{taskId}/{isPass}")
    public void spareAndLubIsThrough(@PathVariable String taskId, @PathVariable Integer isPass,
                                     @RequestHeader String token) {
        applyService.spareAndLubIsThrough(taskId, isPass, 1, token);
    }

    /**
     * 备件出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3
     *
     * @param taskId 任务ID
     * @param isPass 是否通过
     */
    @ApiOperation(value = "备件 出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3")
    @GetMapping(value = "/spareIsThrough/{taskId}/{isPass}")
    public void spareIsThrough(@PathVariable String taskId, @PathVariable Integer isPass,
                               @RequestHeader String token) {
        //审批类型为typeClass 1备件
        applyService.spareAndLubIsThrough(taskId, isPass, 1, token);
    }

    /**
     * 油品 出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3
     *
     * @param taskId 任务ID
     * @param isPass 是否通过
     */
    @ApiOperation(value = "油品 出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3")
    @GetMapping(value = "/lubIsThrough/{taskId}/{isPass}")
    public void lubIsThrough(@PathVariable String taskId, @PathVariable Integer isPass,
                             @RequestHeader String token) {
        //审批类型为typeClass 2油品
        applyService.spareAndLubIsThrough(taskId, isPass, 2, token);
    }

    //------------------------------------= 出库 =----------------------------------------

    /**
     * 备件出库 库管
     *
     * @param processInstanceID 流程ID
     * @param isPass            是否通过
     * @return id
     */
    @Deprecated
    @ApiOperation(value = " 备件出库 库管 ")
    @GetMapping("/spareOut/{processInstanceID}/{isPass}")
    public Integer spareOut(@PathVariable String processInstanceID, @PathVariable Boolean isPass) {
        return applyService.spareAndLubOut(processInstanceID, isPass, 1);
    }

    /**
     * 油品出库 库管
     *
     * @param processInstanceID 流程id
     * @param isPass            是否出库
     * @return id
     */
    @Deprecated
    @ApiOperation(value = " 油品出库 库管 ")
    @GetMapping("/lubOut/{processInstanceID}/{isPass}")
    public Integer lubOut(@PathVariable String processInstanceID, @PathVariable Boolean isPass) {
        return applyService.spareAndLubOut(processInstanceID, isPass, 2);
    }


    /**
     * 驳回出库单
     *
     * @param rejectDTO 驳回信息
     */
    @ApiOperation(value = "驳回出库单")
    @PostMapping("/reject")
    public void reject(@RequestBody RejectDTO rejectDTO) {
        storeHeaderService.reject(rejectDTO);
    }

    /**
     * 出库 当前单号出库 (全部)
     *
     * @param headerId 头表id
     */
    @ApiOperation(value = "出库 全部出库")
    @GetMapping("/deliveryOfCargoFromStorage/{headerId}")
    public void deliveryOfCargoFromStorage(@PathVariable Integer headerId) {
        storeHeaderService.deliveryOfCargoFromStorage(headerId);
    }

    /**
     * 出库 当前单号出库 (全部)
     *
     * @param headerIds 头表id
     */
    @ApiOperation(value = "出库 全部出库")
    @PostMapping("/deliveryOfCargoFromStorageList")
    public void deliveryOfCargoFromStorageList(@RequestBody List<Integer> headerIds) {
        storeHeaderService.deliveryOfCargoFromStorages(headerIds);
    }

    //------------------------------------= 过期 =--------------------------------------------------

    /**
     * 生成唯一不重复的单号 生成规则  年 月 日 流水号（四位）共计12位
     *
     * @param service 服务名
     * @return 唯一单号
     */
    @Deprecated
    @ApiOperation(value = "生成唯一不重复的单号 生成规则  年 月 日 流水号（四位）共计12位")
    @GetMapping(value = "/getSerialNumber/{service}")
    public String getSerialNumber(@PathVariable String service) {
        return applyService.getSerialNumber(service);
    }

    /**
     * 生成唯一不重复的单号 生成规则  年 月 日 时 分 秒 随机数（四位）共计18位
     *
     * @return 唯一单号
     */
    @Deprecated
    @ApiOperation(value = "生成唯yi不重复的单号 生成规则  年 月 日 时 分 秒 随机数（四位）共计18位")
    @GetMapping(value = "/getGodownReceiptNumber")
    public String getGodownReceiptNumber() {
        return applyService.getGodownReceiptNumber();
    }

    //-----------------------------------= 查询 =---------------------------------------------------

    /**
     * 获取全部待出库信息
     *
     * @param headerQueryDTO 行表
     * @return 待出库信息
     */
    @PostMapping("/getHeader")
    @ApiOperation(" 获取全部待出库信息")
    public Page<HeaderDTO> getHeader(@RequestBody HeaderQueryDTO headerQueryDTO) {
        return storeHeaderService.getHeader(headerQueryDTO);
    }

    /**
     * 修改出库信息数据
     *
     * @param lineDTO 行表
     * @return 行表id
     */
    @ApiOperation("修改出库信息数据")
    @PutMapping("/updateHeaderLine")
    public Integer updateHeaderLine(@RequestBody LineDTO lineDTO) {
        return storeHeaderService.updateHeaderLine(lineDTO);
    }

    /**
     * 修改出库信息数据
     *
     * @param lineDTO 行表
     * @return 行表id
     */
    @ApiOperation("修改出库信息数据 批量")
    @PutMapping("/updateHeaderLineList")
    public List<Integer> updateHeaderLineList(@RequestBody List<LineDTO> lineDTO) {
        return storeHeaderService.updateHeaderLineList(lineDTO);
    }

    /**
     * 添加出库信息
     *
     * @param lineDTO 行表
     * @return 行表id
     */
    @ApiOperation("添加出库信息数据")
    @PostMapping("/addHeaderLine")
    public Integer addHeaderLine(@RequestBody LineDTO lineDTO) {
        return storeHeaderService.updateHeaderLine(lineDTO);
    }

    /**
     * 修改备件出库信息
     *
     * @param lineDTOId 备件信息
     */
    @ApiOperation("修改出库信息数据")
    @DeleteMapping("/deleteHeaderLine/{lineDTOId}")
    public void deleteHeaderLine(@PathVariable Integer lineDTOId) {
        storeHeaderService.deleteHeaderLine(lineDTOId);
    }

    /**
     * 获取全部来源
     *
     * @return 来源集合
     */
    @ApiOperation(" 获取全部来源 ")
    @GetMapping("/getAllSource")
    public List<String> getAllSource() {
        return storeHeaderService.getAllSource();
    }

}