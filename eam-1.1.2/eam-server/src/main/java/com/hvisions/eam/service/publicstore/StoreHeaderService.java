package com.hvisions.eam.service.publicstore;

import com.hvisions.eam.activiti.lub.VariablesLubDTO;
import com.hvisions.eam.activiti.sapre.VariablesSpareDTO;
import com.hvisions.eam.client.task.HistoryVariables;
import com.hvisions.eam.dto.publicstore.HeaderDTO;
import com.hvisions.eam.dto.publicstore.LineDTO;
import com.hvisions.eam.dto.publicstore.RejectDTO;
import com.hvisions.eam.query.publicstore.HeaderQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * <p>Title:StoreHeaderService</p>
 * <p>Description:头表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/31</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface StoreHeaderService {

    /**
     * 批量出库
     * @param headerIds 头表列表
     */
    void deliveryOfCargoFromStorages(List<Integer> headerIds);

    /**
     * 通过头表进行出库
     *
     * @param headerId 头表id
     */
    void deliveryOfCargoFromStorage(Integer headerId);

    /**
     * 增加头表 同时增加行表
     *
     * @param variables 头表数据
     * @return 展示信息
     */
    VariablesSpareDTO addHeader(VariablesSpareDTO variables);

    /**
     * 增加头表 同时增加行表
     *
     * @param variables 头表数据
     * @return 展示信息
     */
    VariablesLubDTO addHeader(VariablesLubDTO variables);

    /**
     * 增加头表 同时增加行表
     *
     * @param processInstanceBusinessKey businessKey
     * @param map                        信息
     * @param typeClass                  类型
     */
    void addHeader(String processInstanceBusinessKey, HistoryVariables map, int typeClass);

    /**
     * 查询出库表
     *
     * @param headerQueryDTO 查询条件
     * @return 分页
     */
    Page<HeaderDTO> getHeader(HeaderQueryDTO headerQueryDTO);

    /**
     * 查询出库表 list
     *
     * @param headerQueryDTO 查询条件
     * @return list
     */
    List<HeaderDTO> getHeaderList(HeaderQueryDTO headerQueryDTO);

    /**
     * 修改数据
     *
     * @param lineDTO 行表
     * @return id
     */
    Integer updateHeaderLine(LineDTO lineDTO);

    /**
     * 修改出库信息数据
     *
     * @param lineDTO 行表
     * @return 行表id
     */
    List<Integer> updateHeaderLineList(List<LineDTO> lineDTO);

    /**
     * 删除类型
     *
     * @param lineDTOId id
     */
    void deleteHeaderLine(Integer lineDTOId);

    /**
     * 获取全部来源
     *
     * @return 来源列表
     */
    List<String> getAllSource();

    /**
     * 驳回出库单
     *
     * @param rejectDTO 驳回信息
     */
    void reject(RejectDTO rejectDTO);
}
