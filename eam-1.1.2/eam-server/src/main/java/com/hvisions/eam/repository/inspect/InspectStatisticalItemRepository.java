package com.hvisions.eam.repository.inspect;

import com.hvisions.eam.entity.inspect.HvEamInspectStatisticalItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: InspectStatisticalItemRepository</p >
 * <p>Description: 点检报表子项</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/8</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface InspectStatisticalItemRepository extends JpaRepository<HvEamInspectStatisticalItem, Integer> {
    /**
     * 根据流程id和设备编码查询子项
     *
     * @param equipmentCode     设备编码
     * @param processInstanceId 流程id
     * @return 子项
     */
    List<HvEamInspectStatisticalItem> findAllByEquipmentCodeAndProcessInstanceId(String equipmentCode, String processInstanceId);
}