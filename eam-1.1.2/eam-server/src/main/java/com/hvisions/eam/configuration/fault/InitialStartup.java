package com.hvisions.eam.configuration.fault;

import com.hvisions.eam.service.fault.FaultCheckActivitiBpmnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <p>Title:InitialStartup</p>
 * <p>Description:程序初始启动，检查acticiti流程检查</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/11</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Component
public class InitialStartup implements CommandLineRunner {

    @Autowired
    FaultCheckActivitiBpmnService checkActivitiBpmnService;

    @Override
    public void run(String... args) {
        try {
            checkActivitiBpmnService.checkActivitiBpmn();
        } catch (Exception e) {
            log.info(" ---= 维修流程初始化失败 =---");
        }
    }
}
