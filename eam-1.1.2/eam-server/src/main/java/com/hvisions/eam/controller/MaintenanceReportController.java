package com.hvisions.eam.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.fault.RootEquipmentFaultDTO;
import com.hvisions.eam.service.fault.MaintenanceReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>Title:MaintenanceReportController</p>
 * <p>Description:设备报修</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/1</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@Api(description = "设备报修")
@RequestMapping(value = "/maintenanceReportController")
@Slf4j
public class MaintenanceReportController {

    private final MaintenanceReportService maintenanceReportService;

    @Autowired
    public MaintenanceReportController(MaintenanceReportService maintenanceReportService){
        this.maintenanceReportService = maintenanceReportService;
    }

    /**
     * 设备报修申请
     * @param rootEquipmentFaultDTO 申请单
     * @return 申请单
     */
    @ApiOperation(value = "设备报修申请")
    @ApiResultIgnore
    @PostMapping(value = "/applyEquipmentFault")
    public ResultVO applyEquipmentFault(@RequestBody @Valid RootEquipmentFaultDTO rootEquipmentFaultDTO){
        return maintenanceReportService.applyEquipmentFault(rootEquipmentFaultDTO);
    }


}
