package com.hvisions.eam.controller;

import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.EnumUtil;
import com.hvisions.eam.dto.inspect.plan.InspectPlanDTO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanQueryDTO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanRejectDTO;
import com.hvisions.eam.enums.PlanConditionEnum;
import com.hvisions.eam.service.inspect.InspectPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: InspectPlanController</p >
 * <p>Description: 点巡检计划接口</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/13</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/inspectPlan")
@Api(description = "点巡检计划接口")
public class InspectPlanController {
    private final InspectPlanService inspectPlanService;

    @Autowired
    public InspectPlanController(InspectPlanService inspectPlanService) {
        this.inspectPlanService = inspectPlanService;
    }

    /**
     * 新增点巡检计划（无内容）
     *
     * @param inspectPlanCreateOrUpdateDTO 点巡检计划DTO
     * @return 新增点巡检计划id
     */
    @ApiOperation(value = "新增点巡检计划")
    @PostMapping(value = "/createInspectPlan")
    public Integer createInspectPlan(@RequestBody InspectPlanDTO inspectPlanCreateOrUpdateDTO) {
        return inspectPlanService.createInspectPlan(inspectPlanCreateOrUpdateDTO);
    }

    /**
     * 编辑点巡检计划（无内容）
     *
     * @param inspectPlanCreateOrUpdateDTO 点巡检计划DTO
     * @return 编辑点巡检计划id
     */
    @ApiOperation(value = "更新点巡检计划")
    @PutMapping(value = "/updateInspectPlan")
    public Integer updateInspectPlan(@RequestBody InspectPlanDTO inspectPlanCreateOrUpdateDTO) {

        return inspectPlanService.updateInspectPlan(inspectPlanCreateOrUpdateDTO);
    }

    /**
     * 查询点巡检计划（模糊分页）
     *
     * @param inspectPlanQueryDTO 分页查询条件
     * @return 点巡检计划DTO
     */
    @ApiOperation(value = "根据点巡检计划名称,计划审批状态查询点巡检计划，模糊，分页")
    @PostMapping(value = "/getAllByPlanNameAndConditionId")
    public Page<InspectPlanDTO> getAllByPlanNameAndConditionId(@RequestBody InspectPlanQueryDTO inspectPlanQueryDTO) {
        return inspectPlanService.getPage(inspectPlanQueryDTO);

    }

    /**
     * 批量删除点巡检计划
     *
     * @param idList id集合
     */
    @ApiOperation(value = "批量删除点巡检计划")
    @DeleteMapping(value = "/deleteInspectPlanByIdList")
    public void deleteInspectPlanByIdList(@RequestBody List<Integer> idList) {
        inspectPlanService.deleteInspectPlanByIdList(idList);
    }

    /**
     * 删除点巡检计划
     *
     * @param id 计划id
     */
    @ApiOperation(value = "批量删除点巡检计划")
    @DeleteMapping(value = "/deleteInspectPlanById/{id}")
    public void deleteInspectPlanById(@PathVariable Integer id) {
        List<Integer> ids = new ArrayList<>();
        ids.add(id);
        inspectPlanService.deleteInspectPlanByIdList(ids);
    }


    /**
     * 审批通过计划
     *
     * @param taskIds  任务ids
     * @param userInfo 用户信息
     */
    @ApiOperation(value = "审批通过点巡检计划")
    @PutMapping(value = "/startPlan")
    public void startPlan(@RequestBody List<String> taskIds,
                          @ApiIgnore @UserInfo UserInfoDTO userInfo,
                          @RequestHeader String token) {
        inspectPlanService.startPlanById(taskIds, userInfo, token);
    }

    /**
     * 驳回计划
     *
     * @param inspectPlanRejectDTO 驳回DTO
     * @param userInfo             用户信息
     */
    @ApiOperation(value = "驳回计划")
    @PutMapping(value = "/refusePlan")
    public void refusePlan(@RequestBody InspectPlanRejectDTO inspectPlanRejectDTO,
                           @ApiIgnore @UserInfo UserInfoDTO userInfo,
                           @RequestHeader String token) {
        inspectPlanService.refusePlanById(inspectPlanRejectDTO, userInfo, token);
    }

    /**
     * 强制停用点巡检计划
     *
     * @param ids id列表
     */
    @ApiOperation(value = "强制停用点巡检计划")
    @PutMapping(value = "/forcedStopInspectPlan")
    public void forcedStopInspectPlan(@RequestBody List<Integer> ids) {
        inspectPlanService.forcedStopInspectPlan(ids);
    }

    /**
     * 根据timerId查看多少计划绑定本周期
     *
     * @param timerId timerId
     * @return 次数
     */
    @ApiOperation(value = "根据timerId查看多少计划绑定本周期")
    @GetMapping(value = "/checkBindingByTimerId/{timerId}")
    public Integer checkBindingByTimerId(@PathVariable Integer timerId) {
        return inspectPlanService.checkBindingByTimerId(timerId);
    }

    /**
     * 根据点巡检计划编码查询
     *
     * @param inspectPlanNum 点巡检计划编码
     * @return 保养计划
     */
    @ApiOperation(value = "根据点巡检计划编码查询")
    @GetMapping(value = "/findByInspectPlanNum/{inspectPlanNum}")
    public InspectPlanDTO findByInspectPlanNum(@PathVariable String inspectPlanNum) {
        return inspectPlanService.findByInspectPlanNum(inspectPlanNum);
    }


    /**
     * 通过计划id手动触发计划
     *
     * @param planId 计划id
     */
    @ApiOperation(value = "通过计划id手动触发计划")
    @PutMapping(value = "/triggerPlanById/{planId}")
    public String triggerPlanById(@PathVariable Integer planId) {
        return inspectPlanService.triggerPlanById(planId);
    }


    /**
     * 通过计划id查询详情
     *
     * @param id 计划id
     * @return 计划详情dto
     */
    @ApiOperation(value = "通过计划id查询详情")
    @GetMapping(value = "/getPlanDetailById/{id}")
    public InspectPlanDTO getPlanDetailById(@PathVariable Integer id) {
        return inspectPlanService.getDetail(id);
    }

    /**
     * 发起临时点巡检任务
     *
     * @param dto 点巡检配置项
     * @return 流程实例id
     */
    @ApiOperation(value = "发起临时点巡检任务")
    @PostMapping(value = "createTemporaryTask")
    public String createTemporaryTask(@RequestBody InspectPlanDTO dto) {
        return inspectPlanService.createTemporaryTask(dto);
    }

    /**
     * 获取点巡检计划审批状态
     *
     * @return map
     */
    @ApiOperation(value = "获取点巡检计划状态")
    @GetMapping(value = "/getPlanCondition")
    public Map<Integer, String> getPlanCondition() {
        return EnumUtil.enumToMap(PlanConditionEnum.class);
    }

}