package com.hvisions.eam.service.publicstore;

import com.hvisions.eam.dto.publicstore.MatchingDTO;

import java.util.List;

/**
 * <p>Title:MatchingService</p>
 * <p>Description:匹配规则</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/21</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface MatchingService {

    /**
     * 增加匹配规则
     *
     * @param matchingDTO 匹配规则DTO
     * @return id
     */
    Integer createMatching(MatchingDTO matchingDTO);

    /**
     * 通过匹配规则解析出编码code
     *
     * @param batchNumber 批次号
     * @param service     服务名
     * @return 编码code
     */
    String getCodeByBatchNumber(String batchNumber, String service);

    /**
     * 通过服务名获取解析规则
     *
     * @param service 服务名
     * @return 解析规则
     */
    MatchingDTO getMatchingByService(String service);

    /**
     * 获取全部的服务名
     *
     * @return 服务名list 集合
     */
    List<String> getServiceName();

    /**
     * 删除匹配规则
     *
     * @param id id
     */
    void deleteMatching(Integer id);
}
