package com.hvisions.eam.repository.sprare;

import com.hvisions.eam.entity.spare.HvEamSpareFile;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <p>Title:SpareFileRepository</p>
 * <p>Description:备件文件</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/17</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface SpareFileRepository extends JpaRepository<HvEamSpareFile, Integer> {

    /**
     * 通过备件名称获取所对应的文件
     *
     * @param spareId 备件ID
     * @return 文件列表
     */
    List<HvEamSpareFile> findBySpareId(Integer spareId);

    /**
     * 通过备件ID 和 文件ID查找 关联Id
     *
     * @param spareId 备件id
     * @param fileId  文件id
     * @return 关联id
     */
    HvEamSpareFile findBySpareIdAndFileId(Integer spareId, Integer fileId);

    /**
     * 通过备件id 和 文件id 删除
     *
     * @param spareId 备件id
     * @param fileId  文件id
     */
    void deleteBySpareIdAndFileId(Integer spareId, Integer fileId);
}
