package com.hvisions.eam.repository.lub;

import com.hvisions.eam.entity.lub.HvEamLubToShelve;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title:SpareToShelveEntityRepository</p>
 * <p>Description:油品库位关系</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface LubToShelveEntityRepository extends JpaRepository<HvEamLubToShelve, Integer> {

    /**
     * 通过 备件id 和 库房id 查询是否有数据存在
     *
     * @param lubId    备件id
     * @param shelveId 库房id
     * @return 数据存在
     */
    boolean existsByLubIdAndShelveId(Integer lubId, Integer shelveId);

    /**
     * 通过 备件id 和 库房id 查询批次号信息
     *
     * @param lubId    备件id
     * @param shelveId 库房id
     * @return 批次号信息
     */
    List<HvEamLubToShelve> findByLubIdAndShelveId(Integer lubId, Integer shelveId);


    /**
     * 通过 油品ID 查询 油品ID
     *
     * @param lubId 油品ID
     * @return 库位ID集合
     */
    List<HvEamLubToShelve> findShelveIdByLubId(Integer lubId);

    /**
     * 通过 库位ID 查询 油品ID
     *
     * @param shelveId 库位ID
     * @return 油品ID集合
     */
    List<HvEamLubToShelve> findLubIdByShelveId(Integer shelveId);

    /**
     * 查询 全部 通过 油品ID
     *
     * @param lubId 油品ID
     * @return 全部
     */
    List<HvEamLubToShelve> findAllByLubId(Integer lubId);

    /**
     * 查询 全部 通过 库位ID
     *
     * @param shelveId 库位Id
     * @return 全部
     */
    List<HvEamLubToShelve> findAllByShelveId(Integer shelveId);

    /**
     * 查询 全部信息 通过 油品ID 和 库位ID 和 批次号
     *
     * @param lubId       油品ID
     * @param shelveId    库位ID
     * @param batchNumber 批次号
     * @return 备件库位关系
     */
    HvEamLubToShelve findAllByLubIdAndShelveIdAndBatchNumber(Integer lubId, Integer shelveId, String batchNumber);
    /**
     * 查询 全部信息 通过 油品ID  和 批次号
     *
     * @param lubId       油品ID
     * @param batchNumber 批次号
     * @return 备件库位关系
     */
    HvEamLubToShelve findAllByLubIdAndBatchNumber(Integer lubId, String batchNumber);


}