package com.hvisions.eam.controller;

import com.hvisions.activiti.dto.history.HistoricTaskInstanceDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.common.dto.HvPage;
import com.hvisions.eam.dto.inspect.statistical.InspectHistoryQueryDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalQueryDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalTimeQuantumDTO;
import com.hvisions.eam.service.inspect.InspectStatisticalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: InspectStatisticalController</p >
 * <p>Description: 点检统计控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/8</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/inspectStatistical")
@Api(description = "点检统计接口")
public class InspectStatisticalController {
    private final InspectStatisticalService inspectStatisticalService;

    @Autowired
    public InspectStatisticalController(InspectStatisticalService inspectStatisticalService) {
        this.inspectStatisticalService = inspectStatisticalService;
    }


    /**
     * 点巡检任务完成
     *
     * @param taskId 任务id
     * @param token  token
     */
    @ApiOperation(value = "点巡检任务完成")
    @PutMapping(value = "/completeTaskAndStatistical/{taskId}")
    public void completeTaskAndStatistical(@PathVariable String taskId,@RequestHeader String token) {
        inspectStatisticalService.completeTaskAndStatistical(taskId,token);
    }

    /**
     * 查询统计信息
     *
     * @param inspectStatisticalQueryDTO 查询条件
     * @return 统计信息
     */
    @ApiOperation(value = "查询统计信息")
    @PostMapping(value = "/getStatistical")
    public List<InspectStatisticalDTO> getStatistical(@RequestBody InspectStatisticalQueryDTO inspectStatisticalQueryDTO) {
        return inspectStatisticalService.getStatistical(inspectStatisticalQueryDTO);

    }

    /**
     * 今日点巡检次数
     *
     * @return 今日润滑次数
     */
    @ApiOperation(value = "今日点巡检次数")
    @GetMapping(value = "/getTodayInspectCount")
    public Integer getTodayInspectCount() {
        return inspectStatisticalService.getTodayInspectCount();
    }

    /**
     * 本周点巡检次数
     *
     * @return 次数
     */
    @ApiOperation(value = "本周点巡检次数")
    @GetMapping(value = "/getWeekCount")
    public Integer getWeekCount() {
        return inspectStatisticalService.getWeekCount();
    }

    /**
     * 根据时间段查询点检次数
     *
     * @param inspectStatisticalQueryDTO 查询条件
     * @return list
     */
    @ApiOperation(value = "根据时间段查询点检次数")
    @PostMapping(value = "/getCountByTimeQuantum")
    public List<InspectStatisticalTimeQuantumDTO> getCountByTimeQuantum(@RequestBody InspectStatisticalQueryDTO inspectStatisticalQueryDTO) {
        return inspectStatisticalService.getCountByTimeQuantum(inspectStatisticalQueryDTO);
    }

    /**
     * 根据设备id查已完成的历史任务
     *
     * @param inspectHistoryQueryDTO 查询条件
     * @return 历史
     */
    @ApiOperation(value = "根据设备id查已完成的历史任务 注：此方法不可用businessKey为查询条件")
    @PostMapping(value = "/getHistoryByEquipmentId")
    public List<HistoricTaskInstanceDTO> getHistoryByEquipmentId(@RequestBody InspectHistoryQueryDTO inspectHistoryQueryDTO) {
        return inspectStatisticalService.getHistoryByEquipmentId(inspectHistoryQueryDTO);
    }

    /**
     * 根据设备id查正在进行的任务
     *
     * @param inspectHistoryQueryDTO 查询条件
     * @return 分页
     */
    @ApiOperation(value = "根据设备id查正在进行的任务")
    @PostMapping(value = "/getOngoingByEquipmentId")
    public HvPage<ProcessInstanceDTO> getOngoingByEquipmentId(@RequestBody InspectHistoryQueryDTO inspectHistoryQueryDTO) {
        return inspectStatisticalService.getOngoingByEquipmentId(inspectHistoryQueryDTO);
    }
}