package com.hvisions.eam.repository.maintain;

import com.hvisions.eam.entity.maintain.HvEamMaintainItemEquipment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: MaintainItemFileRepository</p >
 * <p>Description: 保养项目与文件关系Repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/19</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface MaintainItemEquipmentRepository extends JpaRepository<HvEamMaintainItemEquipment, Integer> {
    /**
     * 根据保养项目id删除与文件的关系
     *
     * @param maintainItemId 保养项目id
     */
    void deleteAllByItemId(Integer maintainItemId);

    /**
     * 根据项目id查询
     *
     * @param maintainItemId 保养项目id集合
     * @return 项目—文件实体集合
     */
    List<HvEamMaintainItemEquipment> findAllByItemId(Integer maintainItemId);

    /**
     * 根据保养项目id查询油品备件信息
     *
     * @param maintainItemIdList 保养项目id列表
     * @return 项目-备件集合
     */
    List<HvEamMaintainItemEquipment> getAllByItemIdIn(List<Integer> maintainItemIdList);

}
