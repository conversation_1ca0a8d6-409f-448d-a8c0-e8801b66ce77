package com.hvisions.eam.controller;

import com.hvisions.eam.service.maintain.MaintainTaskService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: MaintainTaskController</p>
 * <p>Description: 保养任务接口</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/5/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping("/maintainTask")
public class MaintainTaskController {
    private final MaintainTaskService service;

    @Autowired
    public MaintainTaskController(MaintainTaskService service) {
        this.service = service;
    }

    /**
     * 批量处理保养任务
     *
     * @param taskIds 任务id列表
     */
    @PostMapping("/finishTask")
    @ApiOperation("批量处理任务")
    public void finishTask(@RequestBody List<String> taskIds) {
        service.finishTask(taskIds);
    }
}









