package com.hvisions.eam.service.publicstore;

import com.hvisions.eam.entity.publicstore.HvEamOutboundCargo;

/**
 * <p>Title:OutboundCargoService</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/22</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface OutboundCargoService {

    /**
     * 是否出库
     * @param processInstanceId 流程id
     * @return 是否出库
     */
    Boolean isOut(String processInstanceId);

    /**
     * 通过流程id 获取出库记录
     * @param processInstanceId 流程id
     * @return 出库记录
     */
    HvEamOutboundCargo findByProcessInsttnceId(String processInstanceId);

    /**
     * 增加
     * @param hvEamOutboundCargo  出库表
     * @return 出库表
     */
    HvEamOutboundCargo save(HvEamOutboundCargo hvEamOutboundCargo);
}
