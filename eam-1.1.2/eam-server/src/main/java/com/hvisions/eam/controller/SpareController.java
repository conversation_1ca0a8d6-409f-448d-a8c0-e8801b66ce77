package com.hvisions.eam.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.consts.SpareConsts;
import com.hvisions.eam.dto.spare.SpareDTO;
import com.hvisions.eam.dto.spare.SpareExtendDTO;
import com.hvisions.eam.query.spare.SpareQueryDTO;
import com.hvisions.eam.service.spare.SpareExtendService;
import com.hvisions.eam.service.spare.SpareFileService;
import com.hvisions.eam.service.spare.SpareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: SpareController</p>
 * <p>Description: 备件</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 * <p>
 * 备件控制类
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/spare")
@Api(description = "备件")
@Slf4j
public class SpareController {

    /**
     * 备件service
     */
    private final SpareService spareService;

    /**
     * 备件下载service
     */
    private final SpareExtendService spareExtendService;

    /**
     * 备件文件service
     */
    private final SpareFileService spareFileService;

    @Autowired
    public SpareController(SpareService spareService,
                           SpareExtendService spareExtendService, SpareFileService spareFileService) {
        this.spareService = spareService;
        this.spareExtendService = spareExtendService;
        this.spareFileService = spareFileService;
    }

    /**
     * 增加备件
     *
     * @param spareDTO 备件DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加备件")
    @PostMapping(value = "/createSpare")
    public Integer createSpare(@RequestBody SpareDTO spareDTO) {
        return spareService.save(spareDTO);
    }

    /**
     * 修改备件 通过备件DTO
     *
     * @param spareDTO 备件DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "修改备件")
    @PutMapping(value = "/updateSpare")
    public Integer updateSpare(@RequestBody SpareDTO spareDTO) {
        return spareService.save(spareDTO);
    }

    /**
     * 查询备件 通过ID
     *
     * @param id 备件DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "查询备件 通过ID")
    @GetMapping(value = "/getSpareById/{id}")
    public SpareDTO getSpareById(@PathVariable Integer id) {
        return spareService.findById(id);
    }

    /**
     * 通过id集合查询备件集合
     *
     * @param ids 备件分页DTO
     * @return 备件集合
     */
    @ApiOperation(value = "通过id集合查询备件集合")
    @PostMapping(value = "/getSpareListByIdList")
    public List<SpareDTO> getSpareListByIdList(@RequestBody List<Integer> ids) {
        return spareService.getSpareListByIdList(ids);
    }

    /**
     * 通过ID删除备件
     *
     * @param id SpareId
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteSpareById/{id}")
    public void deleteSpareById(@PathVariable Integer id) {
        spareService.deleteById(id);
    }

    /**
     * 分页查询
     * 通过 备件编码 备件名称 小类ID 是否关键部位  联合查询
     *
     * @param spareQueryDTO 备件分页DTO
     * @return 分页信息
     */
    @Deprecated
    @ApiOperation(value = "分页查询 通过 备件编码 备件名称 小类ID 是否关键部位  联合查询")
    @PostMapping(value = "/getSpareBySpareCodeAndSpareNameAndSpareTypeSub")
    public Page<SpareDTO> getSpareBySpareCodeAndSpareNameAndSpareTypeSub(@RequestBody SpareQueryDTO spareQueryDTO) {
        return spareService.findAllBySpareCodeAndSpareNameAndSpareTypeSub(spareQueryDTO);
    }

    /**
     * 通过批次号查询备件信息
     *
     * @param batchNumber 批次号
     * @return 备件信息
     */
    @ApiOperation(value = "批次号查询备件")
    @GetMapping("/getSpareDTOByBatchNumber/{batchNumber}")
    public SpareDTO getSpareDTOByBatchNumber(@PathVariable String batchNumber) {
        return spareService.getSpareByBatchNumber("备件", batchNumber);
    }

    /**
     * 导出所有备件信息 1
     *
     * @return 备件信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportSpareLink")
    @ApiOperation(value = "导出所有备件信息,支持超链接")
    public ResponseEntity<byte[]> exportSpareLink() throws IOException, IllegalAccessException {
        return spareExtendService.exportEquipmentLink();
    }

    /**
     * 导出所有备件信息 2
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportEquipment")
    @ApiOperation(value = "导出所有备件信息")
    public ResultVO<ExcelExportDto> exportEquipment() throws IOException, IllegalAccessException {
        return spareExtendService.exportSparePart();
    }

    /**
     * 获取导入模板 1
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getSpareImportTemplateLink")
    @ApiOperation(value = "获取备件导入模板,支持超链接")
    public ResponseEntity<byte[]> getSpareImportTemplateLink() throws IOException, IllegalAccessException {
        return ExcelUtil.generateImportFile(SpareExtendDTO.class, SpareConsts.SPARE_EXPORT_FILE_NAME, null);
    }

    /**
     * 获取导入模板 2
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getSpareImportTemplate")
    @ApiOperation(value = "获取备件导入模板")
    public ResultVO<ExcelExportDto> getSpareImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(SpareExtendDTO.class, SpareConsts.SPARE_EXPORT_FILE_NAME, null);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setFileName(SpareConsts.SPARE_EXPORT_FILE_NAME);
        excelExportDto.setBody(result.getBody());
        return ResultVO.success(excelExportDto);
    }

    /**
     * 导入所有备件信息
     *
     * @param file 备件信息文档
     * @return a
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @EnableFilter
    @PostMapping(value = "/importEquipment")
    @ApiOperation(value = "导入备件信息，如果code存在则更新，code不存在则新增")
    public ImportResult importEquipment(@RequestParam("file") MultipartFile file)
            throws IllegalAccessException, ParseException, IOException {
        return spareExtendService.importEquipment(file);
    }

    /**
     * 通过备件ID获取 相关文件列表
     *
     * @param spareId 备件ID
     * @return 文件ID列表
     */
    @ApiOperation(value = "通过备件ID获取 相关文件列表")
    @GetMapping("/getFileBySpareId/{spareId}")
    public List<Integer> getFileBySpareId(@PathVariable Integer spareId) {
        return spareFileService.getFileBySpareId(spareId);
    }

    /**
     * 通过备件ID添加文件
     *
     * @param spareId 备件ID
     * @param files   文件列表
     * @return 管理学
     */
    @PostMapping("/addFileBySpareId/{spareId}")
    @ApiOperation(value = "通过备件ID添加文件")
    public List<Integer> addFileBySpareId(@PathVariable Integer spareId, @RequestBody List<Integer> files) {
        return spareFileService.addFileBySpareId(spareId, files);
    }

    /**
     * 删除备件文件关系 通过备件id 和 文件id
     *
     * @param spareId 备件id
     * @param fileId  文件id
     */
    @DeleteMapping("/deleteBySpareIdAndFileId")
    @ApiOperation(value = "删除关系")
    public void deleteBySpareIdAndFileId(@PathVariable Integer spareId, @PathVariable Integer fileId) {
        spareFileService.deleteBySpareIdAndFileId(spareId, fileId);
    }

    /**
     * 通过备件编码code 查询备件
     *
     * @param spareCode 编码
     * @return 备件
     */
    @ApiOperation(value = "通过备件编码code 查询备件")
    @PostMapping("/getSpareBySpareCode/{spareCode}")
    public SpareDTO getSpareBySpareCode(@PathVariable String spareCode) {
        return spareService.getSpareBySpareCode(spareCode);
    }
    /**
     * 通过备件编码code 查询备件
     *
     * @param spareCodeList 编码列表
     * @return 备件
     */
    @ApiOperation(value = "通过备件编码code 查询备件")
    @PostMapping("/getSpareBySpareCodeList")
    public List<SpareDTO> getSpareBySpareCodeList(@RequestBody List<String> spareCodeList) {
        return spareService.getSpareBySpareCodeList(spareCodeList);
    }

    /**
     * 分页查询
     *
     * @param spareQueryDTO 查询条件
     * @return 备件信息
     */
    @ApiOperation(value = "分页查询 查询备件")
    @PostMapping("/getSparePageByQuery")
    public Page<SpareDTO> getSparePageByQuery(@RequestBody SpareQueryDTO spareQueryDTO) {
        return spareService.getSparePageByQuery(spareQueryDTO);
    }

}