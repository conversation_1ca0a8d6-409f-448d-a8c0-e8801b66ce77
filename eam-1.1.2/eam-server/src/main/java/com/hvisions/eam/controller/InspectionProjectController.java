package com.hvisions.eam.controller;

import com.hvisions.eam.dto.autonomy.InspectionProjectCreateOrUpdateDTO;
import com.hvisions.eam.dto.autonomy.InspectionProjectDTO;
import com.hvisions.eam.dto.autonomy.InspectionProjectQueryDTO;
import com.hvisions.eam.service.autonomy.InspectionProjectService;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
@Api(tags = "检查项目")
@RestController
@RequestMapping("/inspectionProject")
public class InspectionProjectController {

    private final InspectionProjectService inspectionProjectService;

    @Autowired
    public InspectionProjectController(InspectionProjectService inspectionProjectService) {
        this.inspectionProjectService = inspectionProjectService;
    }

    /**
     * 新增
     *
     * @param dto 新增信息
     * @return int 主键
     * <AUTHOR>
     */
    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public int add(@RequestBody @Valid InspectionProjectCreateOrUpdateDTO dto) {
        return inspectionProjectService.add(dto);
    }

    /**
     * 删除单个
     *
     * @param id 主键
     * <AUTHOR>
     */
    @ApiOperation(value = "删除单个")
    @DeleteMapping("/deleteById/{id}")
    public void deleteById(@PathVariable Integer id) {
        inspectionProjectService.deleteById(id);
    }

    /**
     * 批量删除
     *
     * @param idList 主键列表
     * <AUTHOR>
     */
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteList")
    public void deleteList(@RequestBody List<Integer> idList) {
        inspectionProjectService.deleteList(idList);
    }

    /**
     * 修改
     *
     * @param dto 修改信息
     * @return int 主键
     * <AUTHOR>
     */
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public int update(@RequestBody @Valid InspectionProjectCreateOrUpdateDTO dto) {
        return inspectionProjectService.update(dto);
    }

    /**
     * 分页查询
     *
     * @param dto 查询入参
     * @return 分页对象
     * <AUTHOR>
     */
    @ApiOperation(value = "分页查询")
    @PostMapping("/queryList")
    public Page<InspectionProjectDTO> queryList(@RequestBody @Valid InspectionProjectQueryDTO dto) {
        return inspectionProjectService.queryList(dto);
    }

    /**
     * 详情
     *
     * @param id 主键
     * @return int 主键
     * <AUTHOR>
     */
    @ApiOperation(value = "详情")
    @GetMapping("/getById/{id}")
    public InspectionProjectDTO getById(@PathVariable Integer id) {
        return inspectionProjectService.getById(id);
    }

    /**
     * 下载模板
     *
     * @return 模板
     * <AUTHOR>
     */
    @ApiResultIgnore
    @ApiOperation(value = "下载模板")
    @PostMapping("/getImportTemplate")
    public ResponseEntity<byte[]> getImportTemplate() {
        return inspectionProjectService.getImportTemplate();
    }

    /**
     * 下载模板(给前端使用）
     *
     * @return 模板数据
     */
    @PostMapping(value = "/getImportTemplateForFront")
    @ApiOperation(value = "下载模板给前端使用")
    public ExcelExportDto getImportTemplateForFront() {
        return inspectionProjectService.getImportTemplateForFront();
    }

    /**
     * 导入
     *
     * @param file 文件
     * <AUTHOR>
     *
     * @return 导入结果
     */
    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public ImportResult importExcel(MultipartFile file) {
        return inspectionProjectService.importExcel(file);
    }

    /**
     * 导出
     *
     * @param dto 入参
     * @return 数据文件
     * <AUTHOR>
     */
    @ApiResultIgnore
    @ApiOperation(value = "导出")
    @PostMapping("/exportExcel")
    public ResponseEntity<byte[]> exportExcel(@RequestBody @Valid InspectionProjectQueryDTO dto) {
        return inspectionProjectService.exportExcel(dto);
    }

    /**
     * 导出(给前端使用）
     *
     * @param dto 入参
     * @return 数据文件
     */
    @PostMapping(value = "/exportExcelForFront")
    @ApiOperation(value = "导出给前端使用")
    public ExcelExportDto exportExcelForFront(@RequestBody @Valid InspectionProjectQueryDTO dto) {
        return inspectionProjectService.exportExcelForFront(dto);
    }

    /**
     * 获取检测周期
     *
     * @return 检测周期
     * <AUTHOR>
     */
    @ApiOperation(value = "获取检测周期")
    @GetMapping("/getTestCycle")
    public List<String> getTestCycle() {
        return inspectionProjectService.getTestCycle();
    }

}

