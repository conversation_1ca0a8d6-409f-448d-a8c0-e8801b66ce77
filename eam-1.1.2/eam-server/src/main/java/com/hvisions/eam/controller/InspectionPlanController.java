package com.hvisions.eam.controller;

import com.hvisions.eam.dto.autonomy.InspectionPlanCreateOrUpdateDTO;
import com.hvisions.eam.dto.autonomy.InspectionPlanDTO;
import com.hvisions.eam.dto.autonomy.InspectionPlanQueryDTO;
import com.hvisions.eam.service.autonomy.InspectionPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
@Api(tags = "检查计划")
@RestController
@RequestMapping("/inspectionPlan")
public class InspectionPlanController {

    private final InspectionPlanService inspectionPlanService;

    @Autowired
    public InspectionPlanController(InspectionPlanService inspectionPlanService) {
        this.inspectionPlanService = inspectionPlanService;
    }

    /**
     * 新增
     * <AUTHOR>
     * @param dto 新增信息
     * @return int 主键
     */
    @ApiOperation(value = "新增")
    @PostMapping("/add")
    public int add(@RequestBody @Valid InspectionPlanCreateOrUpdateDTO dto){
        return inspectionPlanService.add(dto);
    }

    /**
     * 删除单个
     * <AUTHOR>

     * @param id 主键
     */
    @ApiOperation(value = "删除单个")
    @DeleteMapping("/deleteById/{id}")
    public void deleteById(@PathVariable Integer id) {
        inspectionPlanService.deleteById(id);
    }

    /**
     * 批量删除
     * <AUTHOR>

     * @param idList 主键list
     */
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteList")
    public void deleteList(@RequestBody List<Integer> idList){
        inspectionPlanService.deleteList(idList);
    }

    /**
     * 修改
     * <AUTHOR>
     * @param dto 修改信息
     * @return int 主键
     */
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public int update(@RequestBody @Valid InspectionPlanCreateOrUpdateDTO dto){
        return inspectionPlanService.update(dto);
    }

    /**
     * 分页查询
     * <AUTHOR>
     * @param dto 查询入参
     * @return 分页对象
     */
    @ApiOperation(value = "分页查询")
    @PostMapping("/queryList")
    public Page<InspectionPlanDTO> queryList(@RequestBody @Valid InspectionPlanQueryDTO dto) {
        return inspectionPlanService.queryList(dto);
    }

    /**
     * 详情
     * <AUTHOR>
     * @param id 主键
     * @return int 主键
     */
    @ApiOperation(value = "详情")
    @GetMapping("/getById/{id}")
    public InspectionPlanDTO getById(@PathVariable Integer id){
        return inspectionPlanService.getById(id);
    }

    /**
     *通过计划id手动触发计划
     * <AUTHOR>
     * @param planId 计划id
     */
    @ApiOperation(value = "通过计划id手动触发计划")
    @PutMapping("/triggerPlanById/{planId}")
    public void triggerPlanById(@PathVariable Integer planId) {
        inspectionPlanService.triggerPlanById(planId);
    }

    /**
     *创建临时任务
     * <AUTHOR>
     * @param dto 入参dto
     */
    @ApiOperation(value = "创建临时任务")
    @PostMapping("/createTemporaryTask")
    public void createTemporaryTask(@RequestBody InspectionPlanCreateOrUpdateDTO dto) {
        inspectionPlanService.createTemporaryTask(dto);
    }

}

