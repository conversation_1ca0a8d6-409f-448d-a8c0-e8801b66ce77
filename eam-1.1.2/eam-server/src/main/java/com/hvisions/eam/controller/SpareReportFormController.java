package com.hvisions.eam.controller;

import com.hvisions.eam.dto.repair.spare.*;
import com.hvisions.eam.service.maintain.SpareReportFormService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title: spareReportForm</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/1/4</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/spareReportForm")
@Api(description = "设备备件报表")
public class SpareReportFormController {

    @Autowired
    SpareReportFormService spareReportFormService;

    /**
     * 备件使用统计
     *
     * @param sparePartUsageQueryDTO 备件使用统计查询条件
     * @return 备件使用统计记录
     */
    @PostMapping(value = "/sparePartUsage")
    @ApiOperation(value = "备件使用统计")
    public List<SparePartUsageDTO> sparePartUsage(@RequestBody SparePartUsageQueryDTO sparePartUsageQueryDTO) {
        return spareReportFormService.sparePartUsage(sparePartUsageQueryDTO);
    }

    /**
     * 备件周转率
     *
     * @param turnoverQueryDTO 周转率查询条件
     * @return 周转率信息
     */
    @PostMapping(value = "/spareTurnover")
    @ApiOperation(value = "备件周转率（月）")
    public List<MonthTurnoverDTO> spareTurnover(@RequestBody TurnoverQueryDTO turnoverQueryDTO) {
        return spareReportFormService.spareTurnover(turnoverQueryDTO);
    }

    /**
     * 当月故障统计报表
     *
     * @param equipmentFailureQueryDTO 查询条件
     * @return 统计记录
     */
    @PostMapping(value = "/equipmentFailure")
    @ApiOperation(value = "当月故障统计报表")
    public List<EquipmentFailureDTO> equipmentFailure(@RequestBody EquipmentFailureQueryDTO equipmentFailureQueryDTO) {
        return spareReportFormService.equipmentFailure(equipmentFailureQueryDTO);
    }

}