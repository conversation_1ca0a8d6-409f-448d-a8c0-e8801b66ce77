package com.hvisions.eam.controller;

import com.hvisions.eam.dto.publicstore.UnitDTO;
import com.hvisions.eam.service.publicstore.SpareUnitService;
import com.hvisions.eam.query.spare.SpareUnitQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title:SpareUnitController</p>
 * <p>Description:备件单位</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@RequestMapping(value = "/spareUnit")
@Api(description = "备件单位")
@Slf4j
public class SpareUnitController {

    private SpareUnitService spareUnitService;

    @Autowired
    public SpareUnitController(SpareUnitService spareUnitService) {
        this.spareUnitService = spareUnitService;
    }

    /**
     * 增加备件单位  改 通过 备件单位DTO
     *
     * @param unitDTO 备件单位DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加备件单位")
    @PostMapping(value = "/createSpareUnit")
    public Integer createSpareUnit(@RequestBody UnitDTO unitDTO) {
        return spareUnitService.save(unitDTO);
    }

    /**
     * 修改备件单位 改 通过 备件单位DTO
     *
     * @param unitDTO 备件单位DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "修改备件单位")
    @PutMapping(value = "/updateSpareUnit")
    public Integer updateSpareUnit(@RequestBody UnitDTO unitDTO) {
        return spareUnitService.save(unitDTO);
    }

    /**
     * 查询备件单位 通过ID
     *
     * @param id 备件单位DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "查询备件单位 通过ID")
    @GetMapping(value = "/getSpareUnitById/{id}")
    public UnitDTO getSpareUnitById(@PathVariable Integer id) {
        return spareUnitService.findById(id);
    }

    /**
     * 删除通过ID
     *
     * @param id 单位ID
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteSpareUnitById/{id}")
    public void deleteSpareUnitById(@PathVariable Integer id) {
        spareUnitService.deleteById(id);
    }

    /**
     * 分页查询 通过单位名称
     *
     * @param spareUnitQueryDTO 单位DTO
     * @return 分页信息
     */
    @ApiOperation(value = "分页查询 通过单位DTO")
    @PostMapping(value = "/getSpareUnitPageBySpareName")
    public Page<UnitDTO> getSpareUnitPageBySpareName(@RequestBody SpareUnitQueryDTO spareUnitQueryDTO) {
        return spareUnitService.findAllByUnitName(spareUnitQueryDTO);
    }

    /**
     * 通过单位名称查询单位
     *
     * @param unitName 单位名称
     * @return 单位
     */
    @ApiOperation(value = "分页查询 通过备件单位名称 如果没查到返回null")
    @GetMapping(value = "/getSpareUnitByUnitName/{unitName}")
    public UnitDTO getSpareUnitByUnitName(@PathVariable String unitName) {
        return spareUnitService.findByUnitName(unitName);
    }


}