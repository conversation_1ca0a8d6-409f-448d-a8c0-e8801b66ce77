package com.hvisions.eam.repository.sprare;


import com.hvisions.eam.entity.spare.HvEamSpareType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title:SpareTypeEntityRepository</p>
 * <p>Description:备件类型</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface SpareTypeEntityRepository extends JpaRepository<HvEamSpareType, Integer> {
    /**
     * 通过父级ID查询 子级全部信息
     *
     * @param parentId 父级ID
     * @return 子级全部信息
     */
    List<HvEamSpareType> findAllByParentId(Integer parentId);


    /**
     * 查询是否有子类
     * @param parentId 父级id
     * @return 是否有子类
     */
    Boolean existsByParentId(Integer parentId);

    /**
     * 通过名称查询
     *
     * @param typeName 类型名称
     * @return 类型
     */
    HvEamSpareType findByTypeName(String typeName);

    /**
     * 通过类型code 获取 类型
     *
     * @param typeCode 类型code
     * @return 类型
     */
    HvEamSpareType findByTypeCode(String typeCode);
}
