package com.hvisions.eam.controller;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.HvExportUtil;
import com.hvisions.eam.dao.ShutDownMapper;
import com.hvisions.eam.dto.report.*;
import com.hvisions.eam.dto.report.SummaryDTO;
import com.hvisions.eam.dto.shutdown.ShutdownDTO;
import com.hvisions.eam.dto.shutdown.ShutdownQuery;
import com.hvisions.eam.service.maintain.ReportStatisticalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: ReportStatisticalController</p >
 * <p>Description: 报表统计</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/1/4</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/reportStatistical")
@Api(description = "报表统计接口")
public class ReportStatisticalController {
    @Autowired
    ShutDownMapper mapper;
    @Autowired
    ReportStatisticalService reportStatisticalService;
    @Autowired
    StringRedisTemplate redisTemplate;
    private String key = "hvisions::equipment::shutdownTargetRate";

    @ApiOperation(value = "根据班组Id，开始时间结束时间范围，查询员工所有维修维保信息")
    @PostMapping(value = "/getAllByCrewIdAndTimeFilter")
    public List<ReportRepairUserJobStatisticalDTO> getAllByCrewIdAndTimeFilter(@RequestBody ReportRepairUserJobStatisticalQuery reportRepairUserJobStatisticalQuery) {
        return reportStatisticalService.repairUserJobStatistical(reportRepairUserJobStatisticalQuery);
    }

    @ApiOperation(value = "根据开始时间和结束时间，查询设备故障统计")
    @PostMapping(value = "/getAllByStartTimeAndEndTimeFromFault")
    public List<ReportEquipmentFaultStatisticalDTO> getAllByStartTimeAndEndTimeFromFault(@RequestBody ReportEquipmentFaultStatisticalQuery reportEquipmentFaultStatisticalQuery) {
        return reportStatisticalService.equipmentFaultStatistical(reportEquipmentFaultStatisticalQuery);
    }

    @ApiOperation(value = "根据开始时间和结束时间，查询急维修Pareto分析统计")
    @PostMapping(value = "/getAllByStartTimeAndEndTimeFromPareto")
    public List<ReportUrgentRepairParetoAnalysisDTO> getAllByStartTimeAndEndTimeFromPareto(@RequestBody ReportUrgentRepairParetoAnalysisQuery reportUrgentRepairParetoAnalysisQuery) {
        return reportStatisticalService.urgentRepairParetoAnalysis(reportUrgentRepairParetoAnalysisQuery);
    }

    @ApiOperation(value = "根据开始时间和结束时间，查询设备停机率折线图统计")
    @PostMapping(value = "/getAllByStartTimeAndEndTimeFromDowntime")
    public List<ReportEquipmentDowntimeRateDTO> getAllByStartTimeAndEndTimeFromDowntime(@RequestBody ReportEquipmentDowntimeRateQuery reportEquipmentDowntimeRateQuery) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        reportEquipmentDowntimeRateQuery.setStartTime(
            format.parse(format.format(reportEquipmentDowntimeRateQuery.getStartTime())));
        ShutdownQuery query = new ShutdownQuery();
        query.setBeginTime(reportEquipmentDowntimeRateQuery.getStartTime());
        query.setEndTime(reportEquipmentDowntimeRateQuery.getEndTime());
        List<ShutdownDTO> infos = mapper.getAll(query);
        List<ReportEquipmentDowntimeRateDTO> result = new ArrayList<>();
        Date dayBegin = null;
        Date dayEnd;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(reportEquipmentDowntimeRateQuery.getStartTime());
        int total = 0;
        for (int i = 0; i < 7; i++) {
            if (dayBegin == null) {
                dayBegin = reportEquipmentDowntimeRateQuery.getStartTime();
            } else {
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                dayBegin = calendar.getTime();
            }
            Calendar cal2 = Calendar.getInstance();
            cal2.setTime(dayBegin);
            cal2.add(Calendar.DAY_OF_MONTH, 1);
            dayEnd = cal2.getTime();
            Date finalDayBegin = dayBegin;
            Date finalDayEnd = dayEnd;
            int sum = infos.stream()
                .filter(t -> t.getShutdownDate().compareTo(finalDayBegin) >= 0 && t.getShutdownDate().compareTo(finalDayEnd) < 0)
                .mapToInt(ShutdownDTO::getDuration)
                .sum();
            ReportEquipmentDowntimeRateDTO dto = new ReportEquipmentDowntimeRateDTO();
            total = total + sum;
            dto.setDays(i + 1);
            dto.setFaultTime(new BigDecimal(sum));
            dto.setPlannedProductionTime(new BigDecimal(8 * 60));
            result.add(dto);
        }
        for (ReportEquipmentDowntimeRateDTO dto : result) {
            dto.setAverageWeeklyFaultRate(new BigDecimal(total * 100 / (8 * 60 * 7)).setScale(2, RoundingMode.HALF_UP));
        }
        return result;
    }

    @ApiOperation(value = "获取目标故障率")
    @GetMapping(value = "/getTarget")
    public Integer getTarget() {
        String s = redisTemplate.opsForValue().get(key);
        if (Strings.isBlank(s)) {
            return 0;
        }
        try {
            return Integer.parseInt(s);
        } catch (NumberFormatException ex) {
            return 0;
        }
    }

    @ApiOperation(value = "设置目标故障率")
    @PostMapping(value = "/setTarget")
    public void setTarget(@RequestParam Integer target) {
        redisTemplate.opsForValue().set(key, target.toString());
    }

    /**
     * 设备维修查询
     *
     * @param reportQuery 查询条件
     * @return 维修信息
     */
    @ApiOperation(value = "设备维修查询")
    @PostMapping(value = "/getReportPageByQuery")
    public Page<MaintainReportDTO> getReportPageByQuery(@RequestBody ReportQuery reportQuery) {
        return reportStatisticalService.getReportPageByQuery(reportQuery);
    }

    /**
     * 导入维修
     *
     * @param file 文件对象
     * @return 导入结果
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ImportResult importMaintainReport(@RequestParam("file") MultipartFile file) throws IllegalAccessException,
        ParseException,
        IOException {
        return reportStatisticalService.importMaintainReport(file);
    }

    @GetMapping(value = "/exportTemplateNew")
    @ApiOperation(value = "excel模板导出")
    public ExcelExportDto exportTemplateNew() {
        return HvExportUtil.exportData(new ArrayList<>(), MaintainReportDTO.class, "MaintainReport", "MaintainReport" +
            ".xls", null);
    }


    /**
     * 获取设备模块整体的 汇总信息
     *
     * @return 汇总信息
     */
    @GetMapping(value = "/getSummary")
    @ApiOperation(value = "获取设备管理模块汇总信息")
    public SummaryDTO getSummary() {
        return reportStatisticalService.getSummary();
    }
}
