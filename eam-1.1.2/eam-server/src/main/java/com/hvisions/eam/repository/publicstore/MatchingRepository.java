package com.hvisions.eam.repository.publicstore;

import com.hvisions.eam.entity.publicstore.HvEamMatching;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title:MatchingRepository</p>
 * <p>Description:正则验证</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/21</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface MatchingRepository extends JpaRepository<HvEamMatching, Integer> {

    /**
     * 通过服务名获取当前匹配规则
     *
     * @param service 服务名
     * @return 匹配规则
     */
    HvEamMatching findByService(String service);

}
