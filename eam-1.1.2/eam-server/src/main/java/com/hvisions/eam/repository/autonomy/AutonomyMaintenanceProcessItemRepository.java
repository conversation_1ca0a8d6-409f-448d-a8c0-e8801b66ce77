package com.hvisions.eam.repository.autonomy;

import com.hvisions.eam.entity.autonomy.HvAmAutonomyMaintenanceProcessItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AutonomyMaintenanceProcessItemRepository extends JpaRepository<HvAmAutonomyMaintenanceProcessItem, Integer> {


    List<HvAmAutonomyMaintenanceProcessItem> getAllByNumberOrderByIdDesc(String numer);

    HvAmAutonomyMaintenanceProcessItem getByProcessDataIdAndNumber(Integer processDataId, String numer);
}









