package com.hvisions.eam.repository.inspect;

import com.hvisions.eam.entity.inspect.HvEamInspectStatistical;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: InspectStatisticalRepository</p >
 * <p>Description: 点检统计</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/8</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface InspectStatisticalRepository extends JpaRepository<HvEamInspectStatistical, Integer> {
    /**
     * 根据设备名称查询统计信息
     *
     * @param equipmentCode  设备编码
     * @param finishedBefore 开始
     * @param finishedAfter  结束
     * @return 统计信息
     */
    List<HvEamInspectStatistical> findAllByEquipmentCodeAndEndTimeBetween(String equipmentCode, Date finishedBefore, Date finishedAfter);

    /**
     * 根据设备名称，产线，结束时间在XX区间的统计信息
     *
     * @param equipmentName  设备名称
     * @param line           产线
     * @param finishedBefore 时间前
     * @param finishedAfter  时间后
     * @return 统计信息
     */
    List<HvEamInspectStatistical> findAllByEquipmentNameContainsAndLineContainsAndEndTimeBetweenAndEndTimeIsNotNull(String equipmentName,
                                                                                                                    String line, Date finishedBefore, Date finishedAfter);

    /**
     * 根据设备code和流程id获取开始结束时间
     *
     * @param equipmentCode     设备编码Z
     * @param processInstanceId 流程id
     * @return 实体
     */
    HvEamInspectStatistical findByEquipmentCodeAndProcessInstanceIdAndEndTimeIsNotNull(String equipmentCode, String processInstanceId);

    /**
     * 获取时间段内点检记录
     *
     * @param begin 开始时间段
     * @param end   结束时间段
     * @return 记录
     */
    List<HvEamInspectStatistical> findAllByEndTimeBetween(Date begin, Date end);

    /**
     * 根据设备id查流程id
     *
     * @param equipmentId 设备id
     * @return 实体list
     */
    List<HvEamInspectStatistical> findAllByEquipmentIdAndEndTimeIsNotNull(Integer equipmentId);

    /**
     * 根据设备id和流程id查记录
     *
     * @param equipmentId       设备id
     * @param processInstanceId 流程id
     * @return 实体
     */
    HvEamInspectStatistical findByEquipmentIdAndProcessInstanceId(Integer equipmentId, String processInstanceId);

    /**
     * 根据设备id查所有结束时间为空的记录
     *
     * @param equipmentId 设备id
     * @return 实体列表
     */
    List<HvEamInspectStatistical> findAllByEquipmentIdAndEndTimeIsNull(Integer equipmentId);


    /**
     * 根据设备列表和最初的时间找相关的流程记录
     *
     * @param equipmentIds 设备id
     * @param beginTime    开始时间
     * @return 流程记录
     */
    List<HvEamInspectStatistical> findAllByEquipmentIdInAndCreateTimeAfter(List<Integer> equipmentIds, Date beginTime);

}
