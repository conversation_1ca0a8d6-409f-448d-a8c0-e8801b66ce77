package com.hvisions.eam.controller;

import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.eam.dto.inspect.report.InspectMapDO;
import com.hvisions.eam.service.inspect.InspectTaskService;
import com.hvisions.eam.utils.UserIdUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <p>Title: MaintainTaskController</p>
 * <p>Description: 保养任务接口</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/5/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping("/inspectTask")
public class InspectTaskController {
    private final InspectTaskService service;
    private final UserIdUtil userIdUtil;

    @Autowired
    public InspectTaskController(InspectTaskService service, UserIdUtil userIdUtil) {
        this.service = service;
        this.userIdUtil = userIdUtil;
    }

    /**
     * 批量处理保养任务
     *
     * @param taskIds 任务id列表
     */
    @PostMapping("/finishTask")
    @ApiOperation("批量处理任务")
    public void finishTask(@RequestBody List<String> taskIds, @RequestHeader String token) {
        service.finishTask(taskIds, userIdUtil.getUserId(), token);
    }

    /**
     * 导出历史任务信息
     *
     * @return 历史任务信息
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @ApiOperation(value = "导出任务信息")
    public ExcelExportDto exportData() {
        return service.exportData();
    }


    /**
     * 导出未完成任务
     *
     * @return 未完成任务信息
     */
    @RequestMapping(value = "/exportTaskData", method = RequestMethod.POST)
    @ApiOperation(value = "导出未完成任务信息")
    public ExcelExportDto exportTaskData() {
        return service.exportTaskData();
    }


    /**
     * 获取点巡检任务地图
     *
     * @param userInfo 用户信息
     * @return 任务地图信息
     */
    @GetMapping(value = "/getTaskMap")
    @ApiOperation(value = "获取点巡检任务地图")
    public List<InspectMapDO> getTaskMap(@ApiIgnore @UserInfo UserInfoDTO userInfo) {
        if (userInfo == null) {
            throw new BaseKnownException("用户信息获取失败，请重新登录");
        }
        return service.getTaskMap(userInfo.getId());
    }
}









