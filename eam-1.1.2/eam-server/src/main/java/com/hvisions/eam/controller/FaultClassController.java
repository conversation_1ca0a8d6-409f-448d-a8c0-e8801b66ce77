package com.hvisions.eam.controller;


import com.hvisions.eam.dto.fault.FaultClassDTO;
import com.hvisions.eam.service.fault.FaultClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: HvEmFaultClassController</p >
 * <p>Description: 故障类controller</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/26</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/EmFaultClass")
@Slf4j
@Api(description = "故障类controller")
public class FaultClassController {

    /**
     * 故障类jpa
     */
    private final FaultClassService faultClassService;

    @Autowired
    public FaultClassController(FaultClassService faultClassService) {
        this.faultClassService = faultClassService;
    }

    /**
     * 添加故障类型
     *
     * @param faultClassDTO 故障类型DTO
     * @return 故障类型id
     */
    @ApiOperation(value = "新增故障类型")
    @RequestMapping(value = "/addEmFaultClass", method = RequestMethod.POST)
    public FaultClassDTO addFaultClass(@RequestBody FaultClassDTO faultClassDTO) {
        return faultClassService.addOrUpdateFaultClass(faultClassDTO);
    }

    /**
     * 删除故障类型
     *
     * @param id 故障类型id
     */
    @ApiOperation(value = "删除故障类型")
    @RequestMapping(value = "/deleteFaultClass/{id}", method = RequestMethod.DELETE)
    public void deleteFaultClass(@PathVariable Integer id) {
        faultClassService.deleteFaultClass(id);
    }

    /**
     * 通过故障类型名称删除
     *
     * @param faultClassName 故障名称
     */
    @ApiOperation(value = " 通过故障类型名称删除 ")
    @DeleteMapping("/deleteFaultClassByFaultClassName/{faultClassName}")
    public void deleteFaultClassByFaultClassName(@PathVariable String faultClassName) {
        faultClassService.deleteFaultClassByFaultClassName(faultClassName);
    }

    /**
     * 更新故障类型
     *
     * @param faultClassDTO faultClassDTO
     * @return FaultClassDTO
     */
    @ApiOperation(value = "更新故障类型")
    @RequestMapping(value = "updateFaultClass", method = RequestMethod.PUT)
    public FaultClassDTO updateFaultClass(@RequestBody FaultClassDTO faultClassDTO) {
        return faultClassService.addOrUpdateFaultClass(faultClassDTO);
    }

    /**
     * 查询子故障类
     *
     * @param parentId 父类id
     * @return 子故障类集合
     */
    @ApiOperation(value = "查询子故障类")
    @RequestMapping(value = "getChildFaultClass/{parentId}", method = RequestMethod.GET)
    public List<FaultClassDTO> getChildFaultClasses(@PathVariable Integer parentId) {
        return faultClassService.getFaultClassByParentId(parentId);
    }

    /**
     * 根据设备类型id查询故障类型
     *
     * @param equipmentClassId 设备类型id
     * @return 故障类集合
     */
    @Deprecated
    @ApiOperation(value = "根据设备类型id查询故障类型")
    @GetMapping(value = "getFaultClassesByEquipmentClassId/{equipmentClassId}")
    public List<FaultClassDTO> getFaultClassesByEquipmentClassId(@PathVariable Integer equipmentClassId) {
        log.info(" 根据设备类型查询 设备id：" + equipmentClassId);
        return faultClassService.getFaultClassByParentId(0);
//        return faultClassService.getFaultClassByEquipmentClassId(equipmentClassId);
    }

    /**
     * 查询根故障类
     *
     * @return 根故障类集合
     */
    @ApiOperation(value = "根故障类集合")
    @RequestMapping(value = "getRootFaultClass", method = RequestMethod.GET)
    public List<FaultClassDTO> getChildFaultClasses() {
        return faultClassService.getFaultClassByParentId(0);
    }

    /**
     * 根据id查询故障类
     *
     * @param id 故障类id
     * @return 故障类
     */
    @ApiOperation(value = "根据id查询故障类")
    @GetMapping(value = "getFaultClassById/{id}")
    public FaultClassDTO getFaultClassById(@PathVariable Integer id) {
        return faultClassService.getFaultClassById(id);
    }

    /**
     * 通过故障名称查询故障类
     *
     * @param faultClassName 故障名称
     * @return 故障名称
     */
    @ApiOperation(value = "通过故障名称查询故障类")
    @GetMapping(value = "getFaultClass/{faultClassName}")
    public List<FaultClassDTO> getFaultClass(@PathVariable String faultClassName) {
        return faultClassService.getFaultClassByFaultClassName(faultClassName);
    }

}