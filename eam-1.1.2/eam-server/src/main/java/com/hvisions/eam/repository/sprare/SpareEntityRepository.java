package com.hvisions.eam.repository.sprare;

import com.hvisions.eam.entity.spare.HvEamSpare;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <p>Title: SpareEntityRepository</p>
 * <p>Description: 备件</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface SpareEntityRepository extends JpaRepository<HvEamSpare, Integer> {

    /**
     * 通过备件匹配编码查询备件
     *
     * @param spareCode 备件匹配编码列表
     * @return 备件信息
     */
    List<HvEamSpare> findAllBySpareCodeIn(List<String> spareCode);

    /**
     * 通过备件匹配编码查询备件
     *
     * @param spareCode 备件匹配编码
     * @return 备件信息
     */
    HvEamSpare findAllBySpareCode(String spareCode);


    /**
     * 通过单位id查询备件
     *
     * @param unitId 单位id
     * @return 备件
     */
    List<HvEamSpare> findByUnitId(Integer unitId);


    /**
     * 查询是否使用了备件类型
     *
     * @param typeId 类型id
     * @return 是否存在
     */
    Boolean existsBySpareTypeId(Integer typeId);


    /**
     * 查询是否使用了备件品牌
     *
     * @param brand 备件品牌编码
     * @return 是否存在
     */
    Boolean existsByBrand(String brand);
}
