package com.hvisions.eam.repository.maintain;

import com.hvisions.eam.entity.maintain.HvEamContentItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: ContentItemRepository</p >
 * <p>Description: 保养内容-项目关系Repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/20</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface ContentItemRepository extends JpaRepository<HvEamContentItem, Integer> {

    /**
     * 根据保养项目id查询是否存在关联关系
     *
     * @param itemId 保养项目id
     * @return 是否
     */
    boolean existsByItemId(Integer itemId);

}
