package com.hvisions.eam.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareBrandDTO;
import com.hvisions.eam.dto.spare.SpareBrandQuery;
import com.hvisions.eam.service.spare.SpareBrandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;

/**
 * <p>Title: SpareBrandController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/5/11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/spareBrand")
@Api(description = "备件品牌")
@Slf4j
public class SpareBrandController {

    @Autowired
    SpareBrandService spareBrandService;

    /**
     * 新增备件品牌
     *
     * @param spareBrandDTO 备件品牌数据
     * @return 备件品牌信息
     */
    @PostMapping("/createBrand")
    @ApiOperation(value = "新增备件品牌")
    public SpareBrandDTO createBrand(@RequestBody SpareBrandDTO spareBrandDTO) {
        return spareBrandService.createBrand(spareBrandDTO);
    }

    /**
     * 更新备件品牌
     *
     * @param spareBrandDTO 备件品牌数据
     * @return 备件品牌信息
     */
    @PutMapping("/updateBrand")
    @ApiOperation(value = "新增备件品牌")
    public SpareBrandDTO updateBrand(@RequestBody SpareBrandDTO spareBrandDTO) {
        return spareBrandService.updateBrand(spareBrandDTO);
    }

    /**
     * 删除备件品牌信息
     *
     * @param id 备件品牌信息id
     */
    @DeleteMapping("/deleteSpareBrand/{id}")
    @ApiOperation(value = "删除备件品牌信息")
    public void deleteSpareBrand(@PathVariable Integer id) {
        spareBrandService.deleteSpareBrand(id);
    }

    /**
     * 分页查询
     *
     * @param spareBrandQuery 品牌查询条件
     * @return 分页信息
     */
    @PostMapping("/getBrandByQuery")
    @ApiOperation(value = "分页查询")
    public Page<SpareBrandDTO> getBrandByQuery(@RequestBody SpareBrandQuery spareBrandQuery) {
        return spareBrandService.getBrandByQuery(spareBrandQuery);
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getBrandImportTemplate")
    @ApiOperation(value = "获取备件品牌导入模板")
    public ResultVO<ExcelExportDto> getBrandImportTemplate() throws IOException, IllegalAccessException {
        return spareBrandService.getBrandImportTemplate();
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getBrandImportTemplateLink")
    @ApiOperation(value = "获取备件品牌导入模板 超链接")
    public ResponseEntity<byte[]> getBrandImportTemplateLink() throws IOException, IllegalAccessException {
        return spareBrandService.getBrandImportTemplateLink();
    }

    /**
     * 导入所有备件品牌信息信息
     *
     * @param file 设备信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @EnableFilter
    @PostMapping(value = "/importSpareBrand")
    @ApiOperation(value = "导入备件品牌信息，如果code存在则更新，code不存在则新增")
    public ImportResult importSpareBrand(@RequestParam("file") MultipartFile file) throws IllegalAccessException,
            ParseException, IOException {
        return spareBrandService.importSpareBrand(file);
    }

    /**
     * 导出品牌信息
     *
     * @return 品牌信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportSpareBrandLink")
    @ApiOperation(value = "导出品牌信息 超链接")
    public ResponseEntity<byte[]> exportSpareBrandLink() throws IOException, IllegalAccessException {
        return spareBrandService.exportSpareBrandLink();
    }

    /**
     * 导出品牌信息
     *
     * @return 品牌信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportSpareBrand")
    @ApiOperation(value = "导出品牌信息")
    public ResultVO<ExcelExportDto> exportSpareBrand() throws IOException, IllegalAccessException {
        return spareBrandService.exportSpareBrand();
    }

}