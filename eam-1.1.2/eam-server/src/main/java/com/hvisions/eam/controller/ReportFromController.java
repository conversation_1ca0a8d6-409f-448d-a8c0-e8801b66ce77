package com.hvisions.eam.controller;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.eam.enums.ObjectTypeEnum;
import com.hvisions.eam.reportfrom.FaultDrawingBoardDTO;
import com.hvisions.eam.reportfrom.ReportFromDTO;
import com.hvisions.eam.reportfrom.ReportGroupDTO;
import com.hvisions.eam.service.fault.ReportFromService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <p>Title:ReportFromController</p>
 * <p>Description:报表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/4</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@Api(description = "报表 controller")
@RequestMapping(value = "/reportFromController")
@Slf4j
public class ReportFromController {

    private ReportFromService reportFromService;

    @Autowired
    public ReportFromController(ReportFromService reportFromService) {
        this.reportFromService = reportFromService;
    }

    /**
     * 获取设备维修看板
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 看板
     */
    @ApiOperation(value = "获取设备维修看板")
    @GetMapping(value = "/getFaultDrawingBoardDTO")
    public List<FaultDrawingBoardDTO> getFaultDrawingBoardDTO(@RequestParam @ApiParam(value = "开始时间", example = "2017-01-01") String startTime,
                                                              @RequestParam @ApiParam(value = "结束时间", example = "2020-05-01") String endTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date begin, end;
        try {
            begin = simpleDateFormat.parse(startTime);
            end = simpleDateFormat.parse(endTime);
        } catch (Exception e) {
            throw new BaseKnownException(ObjectTypeEnum.START_END_TIME_EXCEPTION);
        }
        return reportFromService.getFaultDrawingBoardDTO(begin, end);
    }


    /**
     * 通过 设备名称 故障等级 故障类型名称 开始时间 结束时间 获取设备信息
     *
     * @param reportFromDTO 报表信息
     * @return 报表信息
     */
    @ApiOperation(value = "通过 设备名称 故障等级 故障类型名称 开始时间 结束时间 获取设备信息")
    @PostMapping(value = "/getReportFromDTO")
    public List<ReportFromDTO> getReportFromDTO(@RequestBody ReportFromDTO reportFromDTO) {
        return reportFromService.getReportFromDTO(reportFromDTO);
    }

    /**
     * 今日故障次数
     *
     * @return 故障次数
     */
    @ApiOperation(value = "今日故障次数")
    @GetMapping(value = "/getTodayEquipmentNumber")
    public Integer getTodayEquipmentNumber() {
        return reportFromService.getTodayEquipmentNumber();
    }

    /**
     * 本周故障次数
     *
     * @return 故障次数
     */
    @ApiOperation(value = "本周故障次数")
    @GetMapping(value = "/getWeekEquipmentNumber")
    public Integer getWeekEquipmentNumber() {
        return reportFromService.getWeekEquipmentNumber();
    }

    /**
     * 通过 设备名称 故障等级 故障类型名称 开始时间 结束时间 获取设备信息 分组
     *
     * @param reportFromDTO 报表信息
     * @return 报表信息
     */
    @ApiOperation(value = "通过 设备名称 故障等级 故障类型名称 开始时间 结束时间 获取设备信息 分组")
    @PostMapping(value = "/getReportGroup")
    public List<ReportGroupDTO> getReportGroup(@RequestBody ReportFromDTO reportFromDTO) {
        return reportFromService.getReportGroup(reportFromDTO);
    }

}
