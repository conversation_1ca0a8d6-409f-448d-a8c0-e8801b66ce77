package com.hvisions.eam.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.EnableFilter;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareTypeDTO;
import com.hvisions.eam.query.spare.SpareTypeQueryDTO;
import com.hvisions.eam.service.spare.SpareTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title:SpareTypeController</p>
 * <p>Description:备件类型 </p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@RequestMapping(value = "/spareType")
@Api(description = "备件类型")
@Slf4j
public class SpareTypeController {

    private final SpareTypeService spareTypeService;

    @Autowired
    public SpareTypeController(SpareTypeService spareTypeService) {
        this.spareTypeService = spareTypeService;
    }

    /**
     * 查询全部
     *
     * @return 分页信息
     */
    @ApiOperation(value = "查询全部")
    @GetMapping(value = "/getSparType")
    public List<SpareTypeDTO> getSparType() {
        return spareTypeService.getAll();
    }

    /**
     * 查询备件类型 通过ID
     *
     * @param id 备件类型DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "通过ID 查询备件类型信息 ")
    @GetMapping(value = "/getSpareType/{id}")
    public SpareTypeDTO getSpareType(@PathVariable Integer id) {
        return spareTypeService.findById(id);
    }


    /**
     * 新增类型
     *
     * @param spareTypeDTO 备件类型DTO
     * @return 新增数据
     */
    @ApiOperation(value = "新增类型")
    @PostMapping(value = "/createAddChildNode")
    public SpareTypeDTO createAddChildNode(@RequestBody SpareTypeDTO spareTypeDTO) {
        return spareTypeService.save(spareTypeDTO);
    }

    /**
     * 修改节点
     *
     * @param spareTypeDTO 备件类型DTO
     * @return 新增数据
     */
    @ApiOperation(value = "修改节点")
    @PutMapping(value = "/updateSpareType")
    public SpareTypeDTO updateSpareType(@RequestBody SpareTypeDTO spareTypeDTO) {
        return spareTypeService.save(spareTypeDTO);
    }

    /**
     * 分页查询 通过类型名称 查询所有的父级
     *
     * @param spareTypeQueryDTO 分页信息
     * @return 分页信息
     */
    @ApiOperation(value = "分页查询 通过类型名称 ")
    @PostMapping(value = "/getSparTypePageByTypeName")
    public Page<SpareTypeDTO> getSparTypePageByTypeName(@RequestBody SpareTypeQueryDTO spareTypeQueryDTO) {
        return spareTypeService.findAllByTypeName(spareTypeQueryDTO);
    }


    /**
     * 通过父级ID查询 子级全部信息
     *
     * @param parentId 父级ID
     * @return 子级全部信息
     */
    @ApiOperation(value = "通过父级ID查询 子级全部信息 ")
    @PostMapping(value = "/getChildNode/{parentId}")
    public List<SpareTypeDTO> getChildNodeBySpareTypeId(@PathVariable Integer parentId) {
        return spareTypeService.getChildNode(parentId);
    }

    /**
     * 通过类型code获取类型
     *
     * @param spareTypeCode 类型code
     * @return 备件类型
     */
    @ApiOperation(value = "通过备件类型code获取")
    @PostMapping(value = "/getSpareTypeBySpareTypeCode/{spareTypeCode}")
    public SpareTypeDTO getSpareTypeBySpareTypeCode(@PathVariable String spareTypeCode) {
        return spareTypeService.getSpareTypeBySpareTypeCode(spareTypeCode);
    }


    /**
     * 删除备件类型 通过ID
     *
     * @param id 备件类型ID
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteSpareTypeById/{id}")
    public void deleteSpareTypeById(@PathVariable Integer id) {
        spareTypeService.deleteById(id);
    }


    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getTypeImportTemplate")
    @ApiOperation(value = "获取导入模板")
    public ResultVO<ExcelExportDto> getTypeImportTemplate() throws IOException, IllegalAccessException {
        return spareTypeService.getTypeImportTemplate();
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/getTypeImportTemplateLink")
    @ApiOperation(value = "获取导入模板 超链接")
    public ResponseEntity<byte[]> getTypeImportTemplateLink() throws IOException, IllegalAccessException {
        return spareTypeService.getTypeImportTemplateLink();
    }

    /**
     * 导入所有品牌信息信息
     *
     * @param file 信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @ApiResultIgnore
    @EnableFilter
    @PostMapping(value = "/importSpareType")
    @ApiOperation(value = "导入信息，如果code存在则更新，code不存在则新增")
    public ResultVO<ImportResult> importSpareType(@RequestParam("file") MultipartFile file) throws IllegalAccessException,
            ParseException, IOException {
        return ResultVO.success(spareTypeService.importSpareType(file));
    }

    /**
     * 导出信息
     *
     * @return 品牌信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportSpareTypeLink")
    @ApiOperation(value = "导出信息 超链接")
    public ResponseEntity<byte[]> exportSpareTypeLink() throws IOException, IllegalAccessException {
        return spareTypeService.exportSpareTypeLink();
    }

    /**
     * 导出信息
     *
     * @return 品牌信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportSpareType")
    @ApiOperation(value = "导出信息")
    public ResultVO<ExcelExportDto> exportSpareBrand() throws IOException, IllegalAccessException {
        return spareTypeService.exportSpareType();
    }


}