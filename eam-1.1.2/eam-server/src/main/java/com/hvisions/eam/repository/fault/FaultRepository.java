package com.hvisions.eam.repository.fault;

import com.hvisions.eam.entity.fault.HvEmFault;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;


/**
 * <p>Title: FaultRepository</p >
 * <p>Description: 故障repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface FaultRepository extends JpaRepository<HvEmFault, Integer> {

    /**
     * 查询一类故障
     *
     * @param faultClassId 故障类id
     * @param pageable     分页参数
     * @return 故障集合
     */
    Page<HvEmFault> findByFaultClassIdEquals(Integer faultClassId, Pageable pageable);

    /**
     * 通过code查询
     *
     * @param code 编码
     * @return 故障
     */
    HvEmFault findByFaultCode(String code);

    /**
     * 通过故障类id查询故障
     * @param faultClassId 故障类id
     * @return 故障
     */
    List<HvEmFault> findByFaultClassId(Integer faultClassId);

}
