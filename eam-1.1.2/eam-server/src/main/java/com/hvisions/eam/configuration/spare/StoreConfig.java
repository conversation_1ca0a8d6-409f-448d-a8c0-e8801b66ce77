package com.hvisions.eam.configuration.spare;

/**
 * <p>Title:StoreConfig</p>
 * <p>Description:备件服务通用常量</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/13</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface StoreConfig {
    /**
     * SuppressWarnings() 注解的压制规则 all 全部
     */
    String RULES = "all";
    /**
     * 字符串 0
     */
    String ZERO = "0";

    /**
     * 备件流程图
     */
    String SPARE_PROCESS_DEFINITION_KEY = "store-spare";

    /**
     * 油品流程图
     */
    String LUB_PROCESS_DEFINITION_KEY = "store-lub";

    /**
     * 流程图结束标识符
     */
    String ACTIVITI_END = "ActivitiEnd";

    String LUB_NAME = "油品";

    Integer SPARE_TYPECLASS = 1;

    Integer LUB_TYPECLASS = 2;

    String sqlserver = "sqlserver";

    String mysql = "mysql";

}
