package com.hvisions.eam.repository.publicstore;

import com.hvisions.eam.entity.publicstore.HvEamUseListing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title:UseListingRepository</p>
 * <p>Description:备件使用清单</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface UseListingRepository extends JpaRepository<HvEamUseListing, Integer> {

    /**
     * 通过关系查询出当前关系下的全部关联项
     *
     * @param processInstanceId 流程定义
     * @param type              类型
     * @param spareId           备件
     * @return 全部关联项
     */
    HvEamUseListing findByProcessInstanceIdAndTypeAndSpareId(String processInstanceId, Integer type, Integer spareId);



}
