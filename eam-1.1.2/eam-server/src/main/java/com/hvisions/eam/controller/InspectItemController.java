package com.hvisions.eam.controller;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.eam.dto.inspect.item.InspectItemDTO;
import com.hvisions.eam.dto.inspect.item.InspectItemQueryDTO;
import com.hvisions.eam.service.inspect.InspectItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: InspectItemController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/inspectItem")
@Api(description = "点检项目接口")
public class InspectItemController {

    private final InspectItemService inspectItemService;

    @Autowired
    public InspectItemController(InspectItemService inspectItemService) {
        this.inspectItemService = inspectItemService;
    }

    /**
     * 新增点检项目
     *
     * @param inspectItemCreateOrUpdateDTO 点检项目dto
     * @return 新增保养项目id
     */
    @ApiOperation(value = "新增点检项目")
    @PostMapping(value = "/createInspectItem")
    public Integer createInspectItem(@RequestBody InspectItemDTO inspectItemCreateOrUpdateDTO) {
        return inspectItemService.save(inspectItemCreateOrUpdateDTO);
    }

    /**
     * 编辑点检项目
     *
     * @param inspectItemCreateOrUpdateDTO 点检项目DTO
     * @return 被编辑的项目id
     */
    @ApiOperation(value = "编辑点检内容")
    @PutMapping(value = "/updateInspectItem")
    public Integer updateInspectItem(@RequestBody InspectItemDTO inspectItemCreateOrUpdateDTO) {
        return inspectItemService.save(inspectItemCreateOrUpdateDTO);
    }


    /**
     * 删除单个
     *
     * @param id 单个点检项目id
     */
    @ApiOperation(value = "删除单个")
    @DeleteMapping(value = "/deleteById/{id}")
    public void deleteById(@PathVariable Integer id) {
        inspectItemService.deleteById(id);
    }

    /**
     * 批量删除
     *
     * @param idList id集合
     */
    @ApiOperation(value = "批量删除")
    @DeleteMapping(value = "/deleteByIdList")
    public void deleteByIdList(@RequestBody List<Integer> idList) {
        inspectItemService.deleteByIdList(idList);
    }


    /**
     * 模糊查询
     *
     * @param inspectItemQueryDTO 查询条件
     * @return 点检项目分页DTO
     */
    @ApiOperation(value = "条件分页查询")
    @PostMapping(value = "/getInspectByQuery")
    public Page<InspectItemDTO> getInspectByQuery(@RequestBody InspectItemQueryDTO inspectItemQueryDTO) {
        return inspectItemService.getInspectByQuery(inspectItemQueryDTO);
    }

    /**
     * 启用/停用点检项目
     *
     * @param id 项目id
     * @return 是否启用
     */
    @ApiOperation(value = "启用/停用点检项目")
    @PutMapping(value = "startUsingById")
    public Boolean startUsingById(@RequestParam Integer id) {
        return inspectItemService.toggle(id);
    }

    /**
     * 根据id获取保养项目详情
     *
     * @param id 保养项目id
     * @return 保养项目信息
     */
    @ApiOperation(value = "根据id获取保养项目信息")
    @GetMapping(value = "/getById/{id}")
    public InspectItemDTO getById(@PathVariable Integer id) {
        return inspectItemService.getById(id);
    }

    /**
     * 根据编码获取保养项目详情
     *
     * @param itemCode 保养项目编码
     * @return 保养项目信息
     */
    @ApiOperation(value = "根据编码获取保养项目信息")
    @GetMapping(value = "/getByCode")
    public InspectItemDTO getByCode(@RequestParam String itemCode) {
        return inspectItemService.getByCode(itemCode);
    }

    @ApiOperation(value = "获取所有周期信息")
    @GetMapping(value = "/getAllCycle")
    public List<String> getAllCycle() {
        return inspectItemService.getAllCycle();
    }

    @PostMapping(value = "/export")
    @ApiOperation(value = "导出点巡检项目")
    public ExcelExportDto exportData(@RequestBody List<Integer> ids) {
        return inspectItemService.exportData(ids);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入点巡检项目")
    public ImportResult importData(@RequestParam("file") MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return inspectItemService.importData(file);
    }

    @PostMapping(value = "/excel/template")
    @ApiOperation(value = "excel模板导出")
    public ExcelExportDto exportTemplate() {
        return inspectItemService.exportTemplate();
    }

}