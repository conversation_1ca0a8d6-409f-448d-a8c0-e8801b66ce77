package com.hvisions.eam.repository.maintain;

import com.hvisions.eam.entity.maintain.HvEamItemLub;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: ItemOilRepository</p >
 * <p>Description: 保养项目-油品Repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/9</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface ItemLubRepository extends JpaRepository<HvEamItemLub,Integer> {
    /**
     * 根据保养项目id查询油品备件信息
     *
     * @param maintainItemId 保养项目id
     * @return 项目-油品集合
     */
    List<HvEamItemLub> getAllByMaintainItemId(Integer maintainItemId);

    /**
     * 根据保养项目id查询油品备件信息
     *
     * @param maintainItemIdList 保养项目id
     * @return 项目-油品集合
     */
    List<HvEamItemLub> getAllByMaintainItemIdIn(List<Integer> maintainItemIdList);
    /**
     * 根据保养项目id删除项目与油品的关系
     *
     * @param maintainItemId 保养项目id
     */
    void deleteAllByMaintainItemId(Integer maintainItemId);
}
