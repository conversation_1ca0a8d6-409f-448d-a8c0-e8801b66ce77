package com.hvisions.eam.service.publicstore.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.dto.publicstore.MatchingDTO;
import com.hvisions.eam.entity.publicstore.HvEamMatching;
import com.hvisions.eam.repository.publicstore.MatchingRepository;
import com.hvisions.eam.service.publicstore.MatchingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>Title:MatchingServiceImpl</p>
 * <p>Description:匹配规则</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/21</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Service
public class MatchingServiceImpl implements MatchingService {

    /**
     * 匹配规则jpa
     */
    private final MatchingRepository matchingRepository;

    @Autowired
    public MatchingServiceImpl(MatchingRepository matchingRepository) {
        this.matchingRepository = matchingRepository;
    }

    /**
     * 增加匹配规则
     *
     * @param matchingDTO 匹配规则DTO
     * @return id
     */
    @Override
    public Integer createMatching(MatchingDTO matchingDTO) {
        HvEamMatching dto = matchingRepository.findByService(matchingDTO.getService());
        if (dto == null) {
            // DTO 中验证格式正确性
            if (matchingDTO.getType() == 1) {
                //位置验证
                matchingDTO.validationTypeLocation();
                return matchingRepository.save(DtoMapper.convert(matchingDTO, HvEamMatching.class)).getId();
            }
            if (matchingDTO.getType() == 2) {
                //正则验证
                matchingDTO.validationTypeRegEx();
                return matchingRepository.save(DtoMapper.convert(matchingDTO, HvEamMatching.class)).getId();
            }
            return null;
        } else {
            // DTO 中验证格式正确性
            if (matchingDTO.getType() == 1) {
                //位置验证
                matchingDTO.validationTypeLocation();
                dto.setStart(matchingDTO.getStart());
                dto.setEnd(matchingDTO.getEnd());
                dto.setType(matchingDTO.getType());
                return matchingRepository.save(DtoMapper.convert(dto, HvEamMatching.class)).getId();
            }
            if (matchingDTO.getType() == 2) {
                //正则验证
                matchingDTO.validationTypeRegEx();
                dto.setRegEx(matchingDTO.getRegEx());
                dto.setType(matchingDTO.getType());
                return matchingRepository.save(DtoMapper.convert(dto, HvEamMatching.class)).getId();
            }
            return null;
        }
    }

    /**
     * 删除匹配规则
     *
     * @param id id
     */
    @Override
    public void deleteMatching(Integer id) {
        matchingRepository.deleteById(id);
    }

    /**
     * 通过匹配规则解析出编码code
     *
     * @param batchNumber 批次号
     * @return 编码code
     */
    @Override
    public String getCodeByBatchNumber(String batchNumber, String service) {
        //通过服务名获取匹配规则
        MatchingDTO matching = this.getMatchingByService(service);
        // DTO 中验证格式正确性
        //验证方法 1
        if (MatchingDTO.LOCATION.equals(matching.getType() + "")) {
            if (batchNumber.length() < matching.end) {
                throw new BaseKnownException(StoreExceptionEnum.BATCH_NUMBER_INFORMATION_EXCEPTION);
            }
            return batchNumber.substring(matching.getStart() - 1, matching.getEnd());
        }
        //验证方法 2
        if (MatchingDTO.REG_EX.equals(matching.getType() + "")) {
            //编译此正则表达式regExp，返回regExp被编译后的pattern
            Pattern pattern = Pattern.compile(matching.getRegEx());
            //获得一个Matcher对象 通过此对象 对字符串进行操作
            Matcher matcher = pattern.matcher(batchNumber);
            //解析字符串
            if (matcher.find()) {
                String code = matcher.group("code");
                return code;
            }
        }

        return null;
    }

    /**
     * 通过服务名获取解析规则
     *
     * @param service 服务名
     * @return 解析规则
     */
    @Override
    public MatchingDTO getMatchingByService(String service) {
        HvEamMatching byService = matchingRepository.findByService(service);
        if (byService == null) {
            throw new BaseKnownException(StoreExceptionEnum.REGEX_IS_NULL);
        }
        return DtoMapper.convert(byService, MatchingDTO.class);
    }

    /**
     * 获取全部的服务名
     *
     * @return 服务名list 集合
     */
    @Override
    public List<String> getServiceName() {
        List<HvEamMatching> all = matchingRepository.findAll();
        List<String> service = new ArrayList<>();
        for (HvEamMatching matching : all) {
            service.add(matching.getService());
        }
        return service;
    }

}
