package com.hvisions.eam.configuration.fault;

import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.EquipmentFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:InitialStartup</p>
 * <p>Description:程序初始启动，自动创建维修人扩展信息</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/11</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Component
public class InitialEquipmentRepaireUser extends SafetyCommandLineRunner {

    @Autowired
    EquipmentFeignClient equipmentClient;

    @Override
    public void callRunner(String... args) {
        ResultVO<List<ExtendColumnInfo>> list = equipmentClient.getEquipmentExtendColumnInfo();
        if (!list.isSuccess()) {
            log.info("初始化设备维修负责人失败：调用设备服务接口异常:{}", list.getMessage());
            return;
        }
        List<ExtendColumnInfo> extendColumnInfos = list.getData();
        if (extendColumnInfos.stream()
                .anyMatch(t -> "hv_response".equals(t.getColumnName()))) {
            log.info("设备负责人扩展字段已经添加，直接返回。");
            return;
        }
        ExtendColumnInfo extendColumnInfo = new ExtendColumnInfo();
        extendColumnInfo.setColumnType("VARCHAR");
        extendColumnInfo.setColumnName("hv_response");
        extendColumnInfo.setChName("维修负责人");
        extendColumnInfo.setEnName("Response");
        ResultVO result = equipmentClient.createEquipmentColumn(extendColumnInfo);
        if (result.isSuccess()) {
            log.info("设备负责人扩展字段创建成功.");
        } else {
            log.info("设备负责人扩展字段创建失败.异常信息：{}", result.getMessage());
        }
    }
}
