package com.hvisions.eam.repository.autonomy;

import com.hvisions.eam.entity.autonomy.HvAmInspectionProject;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InspectionProjectRepository extends JpaRepository<HvAmInspectionProject, Integer>, JpaSpecificationExecutor<HvAmInspectionProject> {

    /**
     * 验证项目编码是否存在
     *
     * @param number 项目编码
     * @return 是否
     */
    boolean existsByNumber(String number);

    /**
     * 获取检查项目信息根据编码
     *
     * @param number 编码
     * @return 检查项目信息
     */
    HvAmInspectionProject getAllByNumber(String number);


    /**
     * @param groupId 组id
     * @return 项目list
     * <AUTHOR>
     */
    List<HvAmInspectionProject> findAllByGroupId(Integer groupId);
}
