package com.hvisions.eam.repository.lub;

import com.hvisions.eam.entity.lub.HvEamLubricating;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: SpareEntityRepository</p>
 * <p>Description: 油品</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface LubEntityRepository extends JpaRepository<HvEamLubricating, Integer> {

    /**
     * 通过油品编码获取油品
     *
     * @param lubCode 油品编码
     * @return 油品
     */
    HvEamLubricating findByLubCode(String lubCode);

    /**
     * 通过单位id找备件
     *
     * @param unitId 单位id
     * @return 备件
     */
    List<HvEamLubricating> findByUnitId(Integer unitId);


    /**
     * 查询油品列表
     *
     * @param codeList 编码列表
     * @return 油品列表
     */
    List<HvEamLubricating> findAllByLubCodeIn(List<String> codeList);

}
