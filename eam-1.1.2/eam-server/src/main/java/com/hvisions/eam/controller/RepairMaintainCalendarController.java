package com.hvisions.eam.controller;

import com.hvisions.eam.dto.maintain.RepairMaintainCalendarCard;
import com.hvisions.eam.enums.RepairMaintainEnum;
import com.hvisions.eam.service.maintain.RepairMaintainCalendarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: RepairMantainCanlendarController</p >
 * <p>Description: 维修保养日历控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/2/7</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@RestController
@Api(tags = "维修保养日历")
public class RepairMaintainCalendarController {

    private final RepairMaintainCalendarService repairMaintainCalendarService;

    @Autowired
    public RepairMaintainCalendarController(RepairMaintainCalendarService repairMaintainCalendarService) {
        this.repairMaintainCalendarService = repairMaintainCalendarService;
    }

    @ApiOperation("获取任务状态")
    @GetMapping("/getTaskStatus")
    public Map<String, Integer> getTaskStatus() {
        Map<String, Integer> taskStatus = new HashMap<>();
        for (RepairMaintainEnum repairMaintainEnum : RepairMaintainEnum.values()) {
            taskStatus.put(repairMaintainEnum.getName(), repairMaintainEnum.getCode());
        }
        return taskStatus;
    }

    @ApiOperation("维修保养任务日历")
    @GetMapping("/getRepairMaintainCalendar")
    public List<RepairMaintainCalendarCard> getRepairMaintainCalendar(@RequestParam(value = "date", required = false)
                                                                      @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                              LocalDate queryDate) throws ParseException {
        return repairMaintainCalendarService.getRepairMaintainCalendar(queryDate);
    }
}
