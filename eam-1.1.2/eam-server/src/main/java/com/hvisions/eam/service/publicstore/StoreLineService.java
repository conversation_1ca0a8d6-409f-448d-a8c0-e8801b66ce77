package com.hvisions.eam.service.publicstore;

import com.hvisions.eam.activiti.lub.ItemsLubDTO;
import com.hvisions.eam.activiti.sapre.ItemsSpareDTO;
import com.hvisions.eam.client.task.Items;
import com.hvisions.eam.dto.publicstore.LineDTO;

import java.util.List;
import java.util.Map;

/**
 * <p>Title:StoreLineService</p>
 * <p>Description:行表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/31</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface StoreLineService {


    /**
     * 添加行表
     *
     * @param headerId 头表id
     * @param items    数据
     */
    void addSpareLine(Integer headerId, List<ItemsSpareDTO> items);

    /**
     * 添加行表
     *
     * @param headerId 头表id
     * @param items    数据
     */
    void addLubLine(Integer headerId, List<ItemsLubDTO> items);

    /**
     * 增加行表
     *
     * @param headerId  头表id
     * @param items     内容项
     * @param typeClass 类型
     */
    void addSpareLine(Integer headerId, List<Items> items, int typeClass);

    /**
     * 通过头表id获取 具体内容
     *
     * @param headerId 头表id
     * @return 内容
     */
    List<LineDTO> getItemByHeaderId(Integer headerId);

    /**
     * 更改状态
     *
     * @param lineDTOS 更改集合
     */
    void deliveryOfCargoFromStorage(List<LineDTO> lineDTOS);

    /**
     * 修改
     *
     * @param lineDTO 行表
     * @return id
     */
    Integer update(LineDTO lineDTO);

    /**
     * 删除
     *
     * @param lineDTOId id
     */
    void delete(Integer lineDTOId);
}
