package com.hvisions.eam.repository.publicstore;

import com.hvisions.eam.entity.publicstore.HvEamStoreLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title:LineRepository</p>
 * <p>Description:行表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/31</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface StoreLineRepository extends JpaRepository<HvEamStoreLine, Integer> {

    /**
     * 通过头表获取 内容
     *
     * @param headerId 头表id
     * @return 内容
     */
    List<HvEamStoreLine> findByHeaderId(Integer headerId);


}
