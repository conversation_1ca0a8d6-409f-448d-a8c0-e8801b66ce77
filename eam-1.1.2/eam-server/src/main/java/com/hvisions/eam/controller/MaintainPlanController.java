package com.hvisions.eam.controller;

import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.utils.EnumUtil;
import com.hvisions.eam.dto.maintain.MaintainPlanDTO;
import com.hvisions.eam.dto.maintain.MaintainPlanQuery;
import com.hvisions.eam.dto.maintain.MaintainPlanRejectDTO;
import com.hvisions.eam.enums.PlanConditionEnum;
import com.hvisions.eam.service.maintain.MaintainPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: HvEamMaintainPlanController</p >
 * <p>Description: 保养计划接口</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/maintainPlan")
@Api(description = "保养计划接口")
public class MaintainPlanController {
    private final MaintainPlanService maintainPlanService;

    @Autowired
    public MaintainPlanController(MaintainPlanService maintainPlanService) {
        this.maintainPlanService = maintainPlanService;
    }

    /**
     * 获取保养保养计划审批状态
     *
     * @return 代码，状态
     */
    @ApiOperation(value = "获取保养计划审批状态")
    @GetMapping(value = "/getPlanCondition")
    public Map<Integer, String> getPlanCondition() {
        return EnumUtil.enumToMap(PlanConditionEnum.class);
    }

    /**
     * 查询保养计划（模糊分页）
     *
     * @param maintainPlanQuery 分页查询条件
     * @return 保养计划DTO
     */
    @ApiOperation(value = "根据保养计划名称，设备名称，计划审批状态查询保养计划，模糊，分页")
    @PostMapping(value = "/getAllByPlanNameAndEquipmentAndConditionId")
    public Page<MaintainPlanDTO> getAllByPlanNameAndEquipmentAndConditionId(@RequestBody MaintainPlanQuery maintainPlanQuery) {
        return maintainPlanService.getAll(maintainPlanQuery);
    }

    /**
     * 查询计划详情
     *
     * @param id 计划id
     * @return 计划详情
     */
    @ApiOperation(value = "根据主键查询保养计划详情")
    @GetMapping(value = "/getById/{id}")
    public MaintainPlanDTO getById(@PathVariable Integer id) {
        return maintainPlanService.getById(id);
    }

    /**
     * 新增保养计划，无保养内容
     *
     * @param maintainPlanDTO 保养计划新增DTO
     * @return id
     */
    @ApiOperation(value = "新增保养计划")
    @PostMapping(value = "/createNewMaintainPlan")
    public Integer createNewMaintainPlan(@RequestBody MaintainPlanDTO maintainPlanDTO) {
        return maintainPlanService.createMaintainPlan(maintainPlanDTO);
    }


    /**
     * 删除单个保养计划
     *
     * @param id 单个计划id
     */
    @ApiOperation(value = "根据id删除单个保养计划")
    @DeleteMapping(value = "/deleteMaintainPlanById/{id}")
    public void deleteMaintainPlanById(@PathVariable Integer id) {
        maintainPlanService.deleteMaintainPlanById(id);
    }

    /**
     * 批量删除保养计划
     *
     * @param idList id集合
     */
    @ApiOperation(value = "批量删除保养计划")
    @DeleteMapping(value = "/deleteMaintainPlanByIdList")
    public void deleteMaintainPlanByIdList(@RequestBody List<Integer> idList) {
        maintainPlanService.deleteMaintainPlanByIdList(idList);
    }

    /**
     * 审批通过计划
     *
     * @param taskIds  任务ids
     * @param userInfo 请求用户信息
     */
    @ApiOperation(value = "审批通过保养计划")
    @PutMapping(value = "/startPlan")
    public void startPlan(@RequestBody List<String> taskIds, @ApiIgnore @UserInfo UserInfoDTO userInfo,
                          @RequestHeader String token) {
        maintainPlanService.startPlanById(taskIds, userInfo, token);
    }

    /**
     * 驳回计划
     *
     * @param maintainPlanRejectDTO 驳回DTO
     * @param userInfo              请求用户信息
     */
    @ApiOperation(value = "驳回计划")
    @PutMapping(value = "/refusePlan")
    public void refusePlan(@RequestBody MaintainPlanRejectDTO maintainPlanRejectDTO,
                           @ApiIgnore @UserInfo UserInfoDTO userInfo,
                           @RequestHeader String token) {
        maintainPlanService.refusePlanById(maintainPlanRejectDTO, userInfo, token);
    }


    /**
     * 编辑保养计划
     *
     * @param maintainPlanDTO 保养计划修改DTO
     * @return 编辑保养计划id
     */
    @ApiOperation(value = "更新保养计划")
    @PutMapping(value = "/updateNewMaintainPlan")
    public Integer updateNewMaintainPlan(@RequestBody MaintainPlanDTO maintainPlanDTO) {
        return maintainPlanService.updateMaintainPlan(maintainPlanDTO);
    }

    /**
     * 强制停用保养计划
     *
     * @param ids id列表
     */
    @ApiOperation(value = "强制停用保养计划")
    @PutMapping(value = "/forcedStopMaintainPlan")
    public void forcedStopMaintainPlan(@RequestBody List<Integer> ids) {
        maintainPlanService.forcedStopMaintainPlan(ids);
    }

    /**
     * 根据周期id查看有多少保养计划绑定
     *
     * @param timerId timerId
     * @return 绑定个数
     */
    @ApiOperation(value = "根据周期id查看有多少保养计划绑定")
    @GetMapping(value = "/checkBindingByTimerId/{timerId}")
    public Integer checkBindingByTimerId(@PathVariable Integer timerId) {
        return maintainPlanService.checkBindingByTimerId(timerId);
    }

    /**
     * 通过计划id手动触发计划
     *
     * @param planId 计划id
     */
    @ApiOperation(value = "通过计划id手动触发计划")
    @PutMapping(value = "/triggerPlanById/{planId}")
    public void triggerPlanById(@PathVariable Integer planId) {
        maintainPlanService.triggerPlanById(planId);
    }

    /**
     * 创建临时任务
     *
     * @param dto 保养计划信息
     */
    @ApiOperation(value = "创建临时任务")
    @PostMapping(value = "/createTemporaryTask")
    public void createTemporaryTask(@RequestBody MaintainPlanDTO dto) {
        maintainPlanService.createTemporaryTask(dto);
    }

}

