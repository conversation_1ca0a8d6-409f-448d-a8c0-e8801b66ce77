package com.hvisions.eam.configuration.autonomy;

import com.hvisions.eam.service.autonomy.CheckActivitiBpmnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <p>Title: InitialStartup</p >
 * <p>Description: 程序初始启动，检查acticiti是否有对应的流程文件,若无则需发布</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
@Slf4j
public class AutonomyInitialStartup implements CommandLineRunner {

    @Autowired
    CheckActivitiBpmnService checkActivitiBpmnService;

    @Override
    public void run(String... args) {
        try {
            checkActivitiBpmnService.checkActivitiBpmn();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }
}