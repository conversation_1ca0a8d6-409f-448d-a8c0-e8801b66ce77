package com.hvisions.eam.repository.fault;

import com.hvisions.eam.entity.fault.HvEmFaultSolution;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;


/**
 * <p>Title: FaultSolutionServiceImpl</p >
 * <p>Description: 解决方案repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface FaultSolutionRepository extends JpaRepository<HvEmFaultSolution, Integer> {
    /**
     * 根据故障原因查询解决方案
     *
     * @param faultReasonId 故障原因id
     * @param pageable      分页对象
     * @return 解决方案集合
     */
    Page<HvEmFaultSolution> findByReasonIdEquals(Integer faultReasonId, Pageable pageable);

    /**
     * 通过编码获取解决方案
     *
     * @param code 编码
     * @return 解决方案
     */
    HvEmFaultSolution findBySolutionCode(String code);

    /**
     * 通过故障原因id 查找故障解决方案
     * @param reasonId 故障原因id
     * @return 故障解决方案
     */
    List<HvEmFaultSolution> findByReasonId(Integer reasonId);


}
