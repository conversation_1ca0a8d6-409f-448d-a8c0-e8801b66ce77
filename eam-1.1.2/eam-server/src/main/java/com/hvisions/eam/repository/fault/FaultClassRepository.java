package com.hvisions.eam.repository.fault;

import com.hvisions.eam.entity.fault.HvEmFaultClass;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <p>Title: FaultClassRepository</p >
 * <p>Description: 故障类repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface FaultClassRepository extends JpaRepository<HvEmFaultClass, Integer> {

    /**
     * 根据父类id查询子故障类
     *
     * @param parentId 父类id
     * @return 故障类集合
     */
    List<HvEmFaultClass> findByParentIdEquals(Integer parentId);


    /**
     * 更具设备类型查询故障类
     *
     * @param equipmentClassId 设备类型id
     * @return 故障类集合
     */
    List<HvEmFaultClass> findByEquipmentClassIdEquals(Integer equipmentClassId);

    /**
     * 通过名称查询故障类
     *
     * @param name 故障类名称
     * @return 故障类
     */
    List<HvEmFaultClass> findByFaultClassName(String name);


}
