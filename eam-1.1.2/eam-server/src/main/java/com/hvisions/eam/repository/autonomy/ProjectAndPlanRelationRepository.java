package com.hvisions.eam.repository.autonomy;

import com.hvisions.eam.entity.autonomy.HvAmProjectAndPlanRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectAndPlanRelationRepository extends JpaRepository<HvAmProjectAndPlanRelation, Integer>, JpaSpecificationExecutor<HvAmProjectAndPlanRelation> {

    /**
     * 通过planId删除信息
     *
     * @param planId 计划ID
     */
    void deleteByPlanId(Integer planId);

    /**
     * 根据项目ID查询信息
     *
     * @param projectId 项目ID
     * @return 项目list
     */
    List<HvAmProjectAndPlanRelation> findAllByProjectId(Integer projectId);

}
