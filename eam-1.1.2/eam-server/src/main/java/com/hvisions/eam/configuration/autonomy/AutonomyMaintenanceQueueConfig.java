package com.hvisions.eam.configuration.autonomy;

import com.hvisions.eam.consts.MessageQueueConsts;
import com.hvisions.eam.service.autonomy.imp.AutonomyMaintenanceDataServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.api.RabbitListenerErrorHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: TopicRabbitConfig</p>
 * <p>Description:绑定工作流的流程结束事件处理业务 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Configuration
@Slf4j
public class AutonomyMaintenanceQueueConfig {

    @Autowired
    AutonomyMaintenanceDataServiceImpl autonomyMaintenanceDataService;

    /**
     * timer-exchange交换机名称
     */
    private final static String AUTONOMY_MAINTENANCE_PROCESS_FINISH_QUEUE = "h-visions.autonomymaintenance.process.finish";
    private final static String ROUTING_KEY = "autonomymaintenance.*";

    /**
     * 监听队列,当流程结束的时候接收响应的消息
     *
     * @param processInstanceId 流程实例id
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = AUTONOMY_MAINTENANCE_PROCESS_FINISH_QUEUE, autoDelete = "false"),
            exchange = @Exchange(value = MessageQueueConsts.PROCESS_INSTANCE_COMPLETE_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = ROUTING_KEY),
            errorHandler = "processFinishErrorHandler"
    )
    public void process(String processInstanceId) {
        autonomyMaintenanceDataService.save(processInstanceId,true);
    }

    @Bean
    public RabbitListenerErrorHandler processFinishErrorHandler() {
        return (message, message1, e) -> {
            autonomyMaintenanceDataService.handleError(message, e);
            return null;
        };
    }

}
