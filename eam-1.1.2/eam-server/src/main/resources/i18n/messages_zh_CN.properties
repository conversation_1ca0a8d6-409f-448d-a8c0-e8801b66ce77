# suppress inspection "UnusedProperty" for whole file
#éç¨å¼å¸¸
SUCCESS=æå
BRAND_USED=åçå·²ç»è¢«ä½¿ç¨ï¼ä¸å¯å é¤
EMPTY_LINE=åºåºä¿¡æ¯ä¸ºç©º
SERVER_ERROR=æå¡å¨å¼å¸¸
JSON_PARSE_ERROR=Jsonè§£æéè¯¯
ILLEGAL_STRING=Jsonè§£æåºéï¼è¯·æ£æ¥jsonç»æ
NULL_RESULT=æ¥è¯¢ä¸ºç©º
VIOLATE_INTEGRITY=è¿åéå®ï¼è¯·æ£æ¥æ¯å¦æéå¤æ°æ®
IMPORT_FILE_NO_SUPPORT=æä»¶ç±»åä¸æ¯æ
IMPORT_SHEET_IS_NULL=æä»¶sheetè¡¨ä¸å­å¨
ENTITY_PROPERTY_NOT_SUPPORT=å®ä½å±æ§ä¸æ¯æï¼è¯·æ£æ¥å¯¼å¥æ°æ®
SAVE_SHOULD_NO_IDENTITY=ä¿å­ä¸åºè¯¥æä¸»é®
UPDATE_SHOULD_HAVE_IDENTITY=æ´æ°åºè¯¥æä¸»é®
CONST_VIOLATE=è¿åéå¶ï¼è¯·æ£æ¥æ°æ®åº
NO_SUCH_ELEMENT=æ°æ®æ¥è¯¢ä¸å­å¨
ENTITY_NOT_EXISTS=å¾æ©å±å¯¹è±¡ä¸å­å¨ï¼è¯·æ¥è¯¢åå¯¹è±¡æ¯å¦å­å¨
DATA_INTEGRITY_VIOLATION=æ°æ®å®æ´æ§éªè¯åºé
COLUMN_PATTERN_ILLEGAL=æ©å±åæ ¼å¼éæ³ï¼åªåè®¸æ°å­ï¼ä¸åçº¿ï¼è±æå­ç¬¦
#èªå®ä¹å¼å¸¸
DEMO_EXCEPTION_ENUM=ç¤ºä¾å¼å¸¸ç±»å
ENUM_NOT_ZERO_EXCEPTION=åºæ¿æ°éä¸ä¸ºé¶ï¼å é¤å¤±è´¥
IN_USE=å½åæ°æ®å·²ä½¿ç¨ï¼ä¸å¯å é¤
NUMBER_EXCEPTION=æ°ééè¯¯
TYPE_IS_NULL_EXCEPTION=ç±»åä¸ºç©º
TYPE_EXCEPTION=ç±»åéè¯¯
SHELVE_NUMBER_EXCEPTION=åºå­æ°ééè¯¯
MAX_OR_MIN_NUMBER_EXCEPTION=æå¤§å¼ææå°å¼æ°å¼éè¯¯
REGEX_IS_NULL=æå¡ä¸å­å¨
BATCH_NUMBER_WITH_REGEX_MATCH_ERROR=æ¹æ¬¡å·ä¸æ­£åå¹ééè¯¯
REG_EX_EXCEPTION=æ­£åè¡¨è¾¾å¼éè¯¯
XXX_SHELVE_NUMBER_EXCEPTION=%såºå­æ°éä¸è¶³
SHELVE_NUMBER_IS_NOT_ZERO_EXCEPTION=å½ååºæ¿ä¸æ¯ç©ºåºæ¿
XXX_APPLY_NUMBER_EXCEPTION=%sç³è¯·æ°ééè¯¯
SHELVE_INFORMATION_EXCEPTION=åºæ¿ä¿¡æ¯éè¯¯
BATCH_NUMBER_INFORMATION_EXCEPTION=æ¹æ¬¡å·ä¿¡æ¯éè¯¯
# æ°æ®éè¯¯
DATE_ERROR_EXCEPTION=æ°æ®éè¯¯
# ç»´ä¿®åå¤±æ
THE_DELIERY_ORDER_IS_INVALID_EXCEPTION=åºåºåæ æ
#
SHELVE_AND_BATCH_NUMBER_NOT_SELECT_SPARE=åºæ¿åæ¹æ¬¡å·æªæ¥è¯¢åºå½å%s
# å½ååºåºåä»¥è¢«ä½¿ç¨
CURRENT_OUTBOUND_ORDER_IS_INVALID=å½ååºåºåæ æ
# å½åç±»åæ°éä¸å¯ä¿®æ¹
CURRENT_NUMBER_OF_TYPES_CANNOT_BE_MODIFIED=æ æ³ä¿®æ¹å½åç±»åçæ°é
# åºæ¿æ¹æ¬¡å·æªæ¥è¯¢å°å½å
SHELVE_AND_BATCH_NUMBER_NOT_SELECT_XXX=åºæ¿æ¹æ¬¡å·æªæ¥è¯¢å°%sæ°æ®
# å½åç±»åæ¹æ¬¡å·ä¸å¯ä¿®æ¹
CURRENT_TYPE_BATCH_NUMBER_CANNOT_BE_MODIFIED=å½åç±»åæ¹æ¬¡å·ä¸å¯ä¿®æ¹
# è¶åºç³è¯·æ°é
BEYOND_THE_NUMBER_OF_APPLICATIONS=è¶åºç³è¯·æ°é
# è¯·éæ©æä½æ°æ®
SELECT_OPERATION_DATA=è¯·éæ©æä½æ°æ®
# æå¤§ææå°åºå­æ°ééè¯¯
MAX_OR_MIN_SHELVE_NUMBER_EXCEPTION=æå¤§ææå°åºå­æ°ééè¯¯
# æ²¡æè¿ä¸ªåä½åç§°çåä½
A_UNIT_WITHOUT_THIS_UNIT_NAME=æ²¡æè¿ä¸ªåä½åç§°çåä½
# ä»·æ ¼æ¯éè¯¯ç
PRICE_IS_WRONG=ä»·æ ¼æ¯éè¯¯ç
# åºå­æ°ééè¯¯
INVENTORY_QUANTITY_ERROR=åºå­æ°ééè¯¯
# æ²¡æå·ææ­¤ç±»ååç§°çç±»å
THERE_IS_NO_TYPE_WITH_THIS_TYPE_NAME=æ²¡æå·ææ­¤ç±»ååç§°çç±»å
# æå­å±çº§ï¼ä¸å¯å é¤
THERE_ARE_SUBIEVEIS_THAT_CANNOT_BE_DELETED=æå­å±çº§ï¼ä¸å¯å é¤
# ä¸æ¯æè¯¥ç±»åæ°æ®åºæ¥è¯¢
NOT_CURRENT_DATABASES_SUPPORTED=ä¸æ¯æè¯¥æ°æ®åºæ¥è¯¢
# å½åæ°æ®å·²å­å¨,è¯·ä¿®æ¹å¯¹åºæ°æ®
THE_CURRENT_DATA_ALREADY_EXISTS_PLEASE_MODIFY_THE_CORRESPONDING_DATA=å½åæ°æ®å·²å­å¨,è¯·ä¿®æ¹å¯¹åºæ°æ®
#å¤ä»¶åç§°ä¸åè®¸ä¿®æ¹
SPARE_NAME_IS_NOT_ALLOWED_TO_BE_MODIFIED=å¤ä»¶ä¸åè®¸ä¿®æ¹
#æ²¹åä¸åè®¸ä¿®æ¹
LUBRICATING_IS_NOT_ALLOWED_TO_BE_MODIFIED=æ²¹åä¸åè®¸ä¿®æ¹

#ä¿å»
# suppress inspection "UnusedProperty" for whole file
#èªå®ä¹å¼å¸¸
START_PLAN_ERROR=å½åè®¡åä¸åè®¸æ­¤æä½ï¼æä½å¤±è´¥
REFUSE_PLAN_ERROR=å½åè®¡åå·²ç»éè¿å®¡æ¹ï¼ä¸åè®¸é©³å
DELETE_PLAN_ERROR=å½åè®¡åæ­£å¨å®¡æ¹æå·²ç»éè¿å®¡æ¹ï¼ä¸åè®¸å é¤
UPDATE_PLAN_ERROR=å½åè®¡åæ­£å¨å®¡æ¹æå·²ç»éè¿å®¡æ¹ï¼ä¸åè®¸ç¼è¾
DELETE_CONTENT_ERROR=å½ååå®¹å·²è¢«æä¸ªè®¡åå ç¨ï¼ä¸åè®¸å é¤
DELETE_ITEM_ERROR=å½åé¡¹ç®å·²è¢«å ç¨ï¼ä¸åè®¸å é¤
RESULT_VO_TRANSFER_ERROR=è·¨æå¡è°ç¨åºç°å¼å¸¸ä¿¡æ¯
INSERT_CODE_ERROR=ç¼ç å·²å­å¨
ITEM_IS_NULL=é¡¹ç®ä¸è½ä¸ºç©º
CHECKER_IS_NULL=è®¡åéè¦å®¡æ¹ï¼å®¡æ ¸äººä¸è½ä¸ºç©º
STOP_ERROR=è®¡åè¿æªçæ
# å¼å§ç»ææ¶é´éè¯¯
START_END_TIME_EXCEPTION=å¼å§ç»ææ¶é´éè¯¯

EQUIPMENT_TYPE_SERVER_ERROR=è®¾å¤ç±»åæå¡å¼å¸¸
EQUIPMENT_TYPE_NOT_EXIST_ERROR=è®¾å¤ç±»åä¸å­å¨
MAINTAIN_ITEM_NOT_EXIST=ä¿å»é¡¹ç®ä¸å­å¨

#ç¹æ£
ASSIGNEE_IS_NULL=æ§è¡äººä¸è½å¨é¨ä¸ºç©º

#èªä¸»æ§ç»´æ¤
PROCESS_INSTANCE_START_FAILED=æµç¨å®ä¾å¯å¨å¤±è´¥
PARENT_GROUP_DOES_NOT_EXIST=ç¶çº§åç»ä¸å­å¨
DATA_IN_WRONG_FORMAT=æ°æ®æ ¼å¼éè¯¯
THE_GROUP_DOES_NOT_EXIST=åç»ä¸å­å¨
INCOMPLETE_DATA=æ°æ®ä¸å®æ´
THE_VALUE_RANGE_OF_ENABLE_FLAG_IS_INCORRECT=å¯ç¨æ å¿çåå¼èå´ä¸æ­£ç¡®
ABNORMAL_TIMES_INCORRECT_VALUE_RANGE=å¼å¸¸æ¬¡æ°åå¼èå´ä¸æ­£ç¡®
