
# suppress inspection "UnusedProperty" for whole file
# å¤ä»¶
SUCCESS=SUCCESS
BRAND_USED=BRAND_USED
EMPTY_LINE=EMPTY_LINE
SERVER_ERROR=SERVER_ERROR
JSON_PARSE_ERROR=JSON_PARSE_ERROR
ILLEGAL_STRING=ILLEGAL_STRING
NULL_RESULT=NULL_RESULT
VIOLATE_INTEGRITY=VIOLATE_INTEGRITY
IMPORT_FILE_NO_SUPPORT=IMPORT_FILE_NO_SUPPORT
IMPORT_SHEET_IS_NULL=IMPORT_SHEET_IS_NULL
ENTITY_PROPERTY_NOT_SUPPORT=ENTITY_PROPERTY_NOT_SUPPORT
SAVE_SHOULD_NO_IDENTITY=SAVE_SHOULD_NO_IDENTITY
UPDATE_SHOULD_HAVE_IDENTITY=UPDATE_SHOULD_HAVE_IDENTITY
CONST_VIOLATE=CONST_VIOLATE
NO_SUCH_ELEMENT=NO_SUCH_ELEMENT
ENTITY_NOT_EXISTS=ENTITY_NOT_EXISTS
DATA_INTEGRITY_VIOLATION=DATA_INTEGRITY_VIOLATION
COLUMN_PATTERN_ILLEGAL=COLUMN_PATTERN_ILLEGAL
#???????
DEMO_EXCEPTION_ENUM=DEMO_EXCEPTION_ENUM
ENUM_NOT_ZERO_EXCEPTION=ENUM_NOT_ZERO_EXCEPTION
IN_USE=IN_USE
NUMBER_EXCEPTION=NUMBER_EXCEPTION
TYPE_IS_NULL_EXCEPTION=TYPE_IS_NULL_EXCEPTION
TYPE_EXCEPTION=TYPE_EXCEPTION
SHELVE_NUMBER_EXCEPTION=SHELVE_NUMBER_EXCEPTION
MAX_OR_MIN_NUMBER_EXCEPTION=MAX_OR_MIN_NUMBER_EXCEPTION
REGEX_IS_NULL=REGEX_IS_NULL
BATCH_NUMBER_WITH_REGEX_MATCH_ERROR=BATCH_NUMBER_WITH_REGEX_MATCH_ERROR
REG_EX_EXCEPTION=REG_EX_EXCEPTION
XXX_SHELVE_NUMBER_EXCEPTION=%s_SHELVE_NUMBER_EXCEPTION
SHELVE_NUMBER_IS_NOT_ZERO_EXCEPTION=SHELVE_NUMBER_IS_NOT_ZERO_EXCEPTION
XXX_APPLY_NUMBER_EXCEPTION=%s_APPLY_NUMBER_EXCEPTION
SHELVE_INFORMATION_EXCEPTION=SHELVE_INFORMATION_EXCEPTION
BATCH_NUMBER_INFORMATION_EXCEPTION=BATCH_NUMBER_INFORMATION_EXCEPTION
# ????
DATE_ERROR_EXCEPTION=DATE_ERROR_EXCEPTION
# ?????
THE_DELIERY_ORDER_IS_INVALID_EXCEPTION=THE_DELIERY_ORDER_IS_INVALID_EXCEPTION
# ???????????????
SHELVE_AND_BATCH_NUMBER_NOT_SELECT_SPARE=SHELVE_AND_BATCH_NUMBER_NOT_SELECT_%s
# ???????
CURRENT_OUTBOUND_ORDER_IS_INVALID=CURRENT_OUTBOUND_ORDER_IS_INVALID
# ??????????
CURRENT_NUMBER_OF_TYPES_CANNOT_BE_MODIFIED=CURRENT_NUMBER_OF_TYPES_CANNOT_BE_MODIFIED
# ???????????
SHELVE_AND_BATCH_NUMBER_NOT_SELECT_XXX=SHELVE_AND_BATCH_NUMBER_NOT_SELECT_%s
# ???????????
CURRENT_TYPE_BATCH_NUMBER_CANNOT_BE_MODIFIED=CURRENT_TYPE_BATCH_NUMBER_CANNOT_BE_MODIFIED
# ??????
BEYOND_THE_NUMBER_OF_APPLICATIONS=BEYOND_THE_NUMBER_OF_APPLICATIONS
# ???????
SELECT_OPERATION_DATA=SELECT_OPERATION_DATA
# ???????????
MAX_OR_MIN_SHELVE_NUMBER_EXCEPTION=MAX_OR_MIN_SHELVE_NUMBER_EXCEPTION
# ???????????
A_UNIT_WITHOUT_THIS_UNIT_NAME=A_UNIT_WITHOUT_THIS_UNIT_NAME
# ??????
PRICE_IS_WRONG=PRICE_IS_WRONG
# ??????
INVENTORY_QUANTITY_ERROR=INVENTORY_QUANTITY_ERROR
# ????????????
THERE_IS_NO_TYPE_WITH_THIS_TYPE_NAME=THERE_IS_NO_TYPE_WITH_THIS_TYPE_NAME
# ?????????
THERE_ARE_SUBIEVEIS_THAT_CANNOT_BE_DELETED=THERE_ARE_SUBIEVEIS_THAT_CANNOT_BE_DELETED
# ???????????
NOT_CURRENT_DATABASES_SUPPORTED=NOT_CURRENT_DATABASES_SUPPORTED
# ???????,???????
THE_CURRENT_DATA_ALREADY_EXISTS_PLEASE_MODIFY_THE_CORRESPONDING_DATA=THE_CURRENT_DATA_ALREADY_EXISTS_PLEASE_MODIFY_THE_CORRESPONDING_DATA
#?????????
SPARE_NAME_IS_NOT_ALLOWED_TO_BE_MODIFIED=SPARE_NAME_IS_NOT_ALLOWED_TO_BE_MODIFIED
#???????
LUBRICATING_IS_NOT_ALLOWED_TO_BE_MODIFIED=LUBRICATING_IS_NOT_ALLOWED_TO_BE_MODIFIED

#ä¿å»
#èªå®ä¹å¼å¸¸ä¿¡æ¯
START_PLAN_ERROR=The current plan does not allow this operation. The operation failed
REFUSE_PLAN_ERROR=The current plan has been approved and cannot be rejected
DELETE_PLAN_ERROR=The current plan is being approved or has been approved and deletion is not allowed
UPDATE_PLAN_ERROR=The current plan is being approved or has been approved. Editing is not allowed
DELETE_CONTENT_ERROR=The current content is already occupied by a plan and cannot be deleted
DELETE_ITEM_ERROR=The current item is already occupied and cannot be deleted
RESULT_VO_TRANSFER_ERROR=Exception information appears across service calls
INSERT_CODE_ERROR=The code already exists
ITEM_IS_NULL=Items cannot be empty
CHECKER_IS_NULL=The auditor cannot be empty
STOP_ERROR=The plan hasn't come into effect yet

# å¼å§ç»ææ¶é´éè¯¯
START_END_TIME_EXCEPTION=START_END_TIME_EXCEPTION

EQUIPMENT_TYPE_SERVER_ERROR=EQUIPMENT_TYPE_SERVER_ERROR
EQUIPMENT_TYPE_NOT_EXIST_ERROR=EQUIPMENT_TYPE_NOT_EXIST_ERROR
MAINTAIN_ITEM_NOT_EXIST=MAINTAIN_ITEM_NOT_EXIST
#ç¹å·¡æ£
ASSIGNEE_IS_NULL=ASSIGNEE_IS_NULL

#èªä¸»æ§ç»´æ¤
PROCESS_INSTANCE_START_FAILED=PROCESS_INSTANCE_START_FAILED
PARENT_GROUP_DOES_NOT_EXIST=PARENT_GROUP_DOES_NOT_EXIST
DATA_IN_WRONG_FORMAT=DATA_IN_WRONG_FORMAT
THE_GROUP_DOES_NOT_EXIST=THE_GROUP_DOES_NOT_EXIST
INCOMPLETE_DATA=INCOMPLETE_DATA
THE_VALUE_RANGE_OF_ENABLE_FLAG_IS_INCORRECT=THE_VALUE_RANGE_OF_ENABLE_FLAG_IS_INCORRECT
ABNORMAL_TIMES_INCORRECT_VALUE_RANGE=ABNORMAL_TIMES_INCORRECT_VALUE_RANGE




