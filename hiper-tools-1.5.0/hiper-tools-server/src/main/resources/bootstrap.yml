#默认公共配置
server:
  port: 9090
spring:
  application:
    name: hiper-tools
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: dev1
      config:
        #nacos中心地址
        server-addr: 127.0.0.1:8848
        # 配置文件格式
        file-extension: yaml
        namespace: dev1
        override-none: true
        extension-configs:
          # 需共享的DataId，yaml后缀不能少，只支持yaml/properties
          # 越靠后，优先级越高
          - data-id: common.yaml
            refresh: true
  datasource:
    initialization-mode: always
    schema:
      - classpath:/sql/quartz-mysql-schema.sql
    continue-on-error: true
  # war包重复部署如果设置开启会有问题。jar包部署可以打开
  jmx:
    enabled: false
  http:
    encoding:
      force: true
    charset: UTF-8
    enabled: true
  tomcat:
    uri-encoding: UTF-8
  #使用redis作为缓存
  cache:
    type: redis
  #序列化时间格式
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
    #是否打印Jpa生成的sql语句
    show-sql: false
    #数据库生成策略，如果打开会根据entity对象生成数据库。生产环境尽量不要使用
    hibernate:
      ddl-auto: update
  #国际化配置
  messages:
    basename: i18n/messages
    cache-seconds: -1
    encoding: utf-8
  # quartz配置
  quartz:
    job-store-type: jdbc
    properties:
      org:
        quartz:
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            clusterCheckinInterval: 5000
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            isClustered: true
            tablePrefix: QRTZ_
            useProperties: false
            acquireTriggersWithinLock: true
          scheduler:
            instanceId: AUTO
            instanceName: HvisionsScheduler
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
ribbon:
  #请求处理的超时时间
  ReadTimeout: 120000
  #请求连接的超时时间
  ConnectTimeout: 30000

pagehelper:
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
  page-size-zero: true

#开启feign的hystrix熔断功能
feign:
  hystrix:
    enabled: false
#开启所有的健康监控信息
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
#info标签：可以在springboot admin的Insights界面Detail中进行展示,也可以再eureka界面点击实例名称查看
info:
  build:
    artifact: '@project.artifactId@'
    version: '@project.version@'
    server-name: '${h-visions.service-name}'

mybatis:
  configuration:
    map-underscore-to-camel-case: true
  typeAliasesPackage: com.hvisions.hipertools.entity
  mapperLocations: classpath:mapper/*.xml
