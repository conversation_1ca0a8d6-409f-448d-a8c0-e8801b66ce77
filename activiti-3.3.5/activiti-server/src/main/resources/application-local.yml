#dev配置
spring:
  datasource:
    #数据库驱动，mysql8.0
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
    url: ***************************************************************************************************
    #支持消息队列
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: admin
  redis:
    host: ************
    port: 6379
h-visions:
  # 消息记录
  activiti:
    #是否在任务创建时发送消息
    send-message: false
  #是否添加所有控制器请求记录到log服务
  log:
    enable: false
  #服务名称,可以使用中文，日志服务会使用
  service-name: 工作流服务
  #可以使用swagger的接口，使用对应的测试方法，生成api文档，支持markdown和ascii
  swagger:
    enable: true
    api-url: http://localhost:${server.port}/v2/api-docs;
    asciidoc-dir: ./build/asciidoc/
    markdown-dir: ./build/markdown/
