{"id": 1, "createTime": "2022-06-02 09:00:15", "updateTime": "2022-06-08 17:03:38", "creatorId": 1, "updaterId": 1, "formCode": "demo-page-form", "formName": "DEMO表单", "numColumns": 1, "description": "以模态框展示为例", "formGroup": "无类型", "popup": "modal", "formControlDTOS": [{"controlType": "UserSelect", "required": true, "key": "UserSelect6", "label": "人员", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择人员"}, "index": null, "properties": {"groupIds": []}, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 0, "y": 0}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": true, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "UserGroupSelect", "required": false, "key": "UserGroupSelect9", "label": "人员组", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择人员组"}, "index": null, "properties": {"groupIds": []}, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 0, "y": 1}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "EquipmentSelect", "required": false, "key": "EquipmentSelect12", "label": "设备", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择设备"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 0, "y": 2}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "MaterialSelect", "required": false, "key": "MaterialSelect15", "label": "物料", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择物料"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 0, "y": 3}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "SupplierSelect", "required": false, "key": "SupplierSelect18", "label": "供应商", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择供应商"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 0, "y": 4}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "CellSelect", "required": false, "key": "CellSelect21", "label": "产线", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择产线"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 0, "y": 5}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "ShiftSelect", "required": false, "key": "ShiftSelect24", "label": "班次", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择班次"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 0, "y": 6}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "CrewSelect", "required": false, "key": "CrewSelect27", "label": "班组", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择班组"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 0, "y": 7}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "PureText", "required": false, "key": "PureText30", "label": "说明", "optionDTOS": [], "attr": {"disabled": false, "placeholder": ""}, "index": null, "properties": null, "layouts": {"static": false, "w": 12, "moved": false, "h": 1, "x": 0, "y": 8}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}, "content": "仅做展示的描述性信息"}, "groupCode": null, "children": null}, {"controlType": "Input", "required": true, "key": "Input34", "label": "文本", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请输入文本"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 6, "y": 0}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": true, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "InputNumber", "required": false, "key": "InputNumber37", "label": "数量", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请输入数字"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 6, "y": 1}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "TimePicker", "required": false, "key": "TimePicker40", "label": "时间", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择时间"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 6, "y": 2}, "setting": {"sourceOrigin": "", "tableDisplay": true, "sourceType": "", "format": "HH:mm:ss", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "DatePicker", "required": false, "key": "DatePicker43", "label": "日期", "optionDTOS": [], "attr": {"showTime": false, "disabled": false, "placeholder": "请选择日期"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 6, "y": 3}, "setting": {"sourceOrigin": "", "tableDisplay": true, "sourceType": "", "format": "YYYY-MM-DD", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "WeekPicker", "required": false, "key": "WeekPicker46", "label": "第几周", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择周"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 6, "y": 4}, "setting": {"sourceOrigin": "", "tableDisplay": true, "sourceType": "", "format": "YYYY-第W周", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "MonthPicker", "required": false, "key": "MonthPicker49", "label": "月份", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择月份"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 6, "y": 5}, "setting": {"sourceOrigin": "", "tableDisplay": true, "sourceType": "", "format": "YYYY-MM", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "RangePicker", "required": false, "key": "RangePicker52", "label": "开始结束时间", "optionDTOS": [], "attr": {"showTime": true, "disabled": false, "placeholder": "请选择日期范围"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 6, "y": 6}, "setting": {"sourceOrigin": "", "tableDisplay": true, "sourceType": "", "format": "YYYY-MM-DD HH:mm:ss", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "Select", "required": false, "key": "Select55", "label": "选项", "optionDTOS": [{"value": "yes", "label": "是"}, {"value": "no", "label": "否"}], "attr": {"mode": "", "showSearch": false, "disabled": false, "placeholder": "请选择选项"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 6, "y": 7}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "custom", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "Radio", "required": false, "key": "Radio58", "label": "单选", "optionDTOS": [{"value": "yes", "label": "是"}, {"value": "no", "label": "否"}], "attr": {"disabled": false, "placeholder": "请选择选项"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 0, "y": 9}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "custom", "format": "", "isQuery": true, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "Checkbox", "required": false, "key": "Checkbox61", "label": "多选", "optionDTOS": [{"value": "A", "label": "选项A"}, {"value": "B", "label": "选项B"}, {"value": "C", "label": "选项C"}], "attr": {"disabled": false, "placeholder": "请选择选项"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 0, "y": 10}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "custom", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "TextArea", "required": false, "key": "TextArea64", "label": "描述", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请输入文本"}, "index": null, "properties": null, "layouts": {"static": false, "w": 6, "moved": false, "h": 1, "x": 0, "y": 11}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "Rate", "required": false, "key": "Rate68", "label": "得分", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请选择评分"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 6, "y": 9}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "ImageUpload", "required": false, "key": "ImageUpload72", "label": "图片", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "支持png、jpg等格式"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 3, "w": 6, "maxH": 3, "moved": false, "h": 3, "x": 6, "y": 10}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}, {"controlType": "FileUpload", "required": false, "key": "FileUpload76", "label": "文件", "optionDTOS": [], "attr": {"disabled": false, "placeholder": "请上传.pdf"}, "index": null, "properties": null, "layouts": {"static": false, "minH": 1, "w": 6, "maxH": 1, "moved": false, "h": 1, "x": 0, "y": 12}, "setting": {"sourceOrigin": "", "tableDisplay": true, "isPassword": false, "sourceType": "", "format": "", "isQuery": false, "rules": {"pattern": "", "message": ""}}, "groupCode": null, "children": null}]}