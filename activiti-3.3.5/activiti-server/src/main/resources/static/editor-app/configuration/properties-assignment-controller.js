/*
 * Activiti Modeler component part of the Activiti project
 * Copyright 2005-2014 Alfresco Software, Ltd. All rights reserved.
 * 
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 */

/*
 * Assignment
 */
var KisBpmAssignmentCtrl = [ '$scope', '$modal', function($scope, $modal) {

    // Config for the modal window
    var opts = {
        template:  'editor-app/configuration/properties/assignment-popup.html?version=' + Date.now(),
        scope: $scope
    };

    // Open the dialog
    $modal(opts);
}];

var KisBpmAssignmentPopupCtrl = [ '$scope', '$http', function($scope, $http) {
	
    // Put json representing assignment on scope
    if ($scope.property.value !== undefined && $scope.property.value !== null
        && $scope.property.value.assignment !== undefined
        && $scope.property.value.assignment !== null) 
    {
        $scope.assignment = $scope.property.value.assignment;
    } else {
        $scope.assignment = {};
    }

    if ($scope.assignment.candidateUsers == undefined || $scope.assignment.candidateUsers.length == 0)
    {
    	$scope.assignment.candidateUsers = [{value: '', name: ''}];
    }
	$scope.assigneeList = [];
	$scope.userGroup = []
	var baseUrl = window.location.origin
	var tokenArr = window.location.search.split('&')[1]
	var token = ''
	if  (tokenArr) {
		token = tokenArr.split('=')[1]
	}

	var loadUsers = function() {
		var url = baseUrl + '/activiti/identity/getAllUser'
		$http({    
			method: 'GET',
			dataType:"json",
            ignoreErrors: true,
            headers: {
				'Accept': 'application/json',
				'Access-Control-Allow-Credentials': true,
				'token': token  
			},
			url: url
		})
		.success(function (data, status, headers, config) {
			if (data.code == 200) {
				$scope.assigneeList = data.data
			}
        })
        .error(function (data, status, headers, config) {
            console.log('Something went wrong when updating the process model:' + JSON.stringify(data));
        });
	}

	var loadUserGroup = function() {
		var url = baseUrl + '/activiti/identity/getGroupByQuery'
		$http({    
			method: 'POST',
            data:  {
				page: 0,
				pageSize: 10000
			},
			dataType:"json",
            ignoreErrors: true,
            headers: {
				'Accept': 'application/json',
				'Access-Control-Allow-Credentials': true,
				'token': token
			},
			url: url
		})
		.success(function (data, status, headers, config) {
			if (data.code == 200) {
				$scope.userGroup = data.data.content
			}
        })
        .error(function (data, status, headers, config) {
            console.log('Something went wrong when updating the process model:' + JSON.stringify(data));
        });
	}

	loadUsers()
	loadUserGroup()
	$scope.fu = function(){
		if ($scope.assignment.assignee) {
			var seletAssignee = $scope.assigneeList.filter(item => item.id == $scope.assignment.assignee)
			if (seletAssignee.length > 0) {
				$scope.assignment.assigneeName = seletAssignee[0]['firstName']
			}
		}
	}
	$scope.fuInput = function(){
		$scope.assignment.assignee = $scope.assignment.assigneeName
	}
	$scope.fuCandidateUser = function(index) {
		var selectData = {...$scope.assignment.candidateUsers[index]}
		var seletAssignee = $scope.assigneeList.filter(item => item.id == selectData.value)
		$scope.assignment.candidateUsers[index] = {
			value: selectData.value,
			name: seletAssignee[0]['firstName']
		}
	}
	
	$scope.fuCandidateUserInput = function(index) {
		var selectData = {...$scope.assignment.candidateUsers[index]}
		if (selectData && selectData.name) {
			$scope.assignment.candidateUsers[index]['value'] = selectData['name'] ? selectData['name'] : ''
		}
	}
	$scope.fuCandidateGroup = function(index) {
		var selectData = {...$scope.assignment.candidateGroups[index]}
		var seletAssignee = $scope.userGroup.filter(item => item.id == selectData.value)
		$scope.assignment.candidateGroups[index] = {
			value: selectData.value,
			name: seletAssignee[0]['name']
		}
	}

	$scope.fuCandidatGroupInput = function(index) {
		var selectData = {...$scope.assignment.candidateGroups[index]}
		if (selectData && selectData.name) {
			$scope.assignment.candidateGroups[index]['value'] = selectData['name'] ? selectData['name'] : ''
		}
	}
	
    // Click handler for + button after enum value
    var userValueIndex = 1;
    $scope.addCandidateUserValue = function(index) {
        $scope.assignment.candidateUsers.splice(index + 1, 0, {value: '', name: ''});
    };

    // Click handler for - button after enum value
    $scope.removeCandidateUserValue = function(index) {
        $scope.assignment.candidateUsers.splice(index, 1);
    };
    
    if ($scope.assignment.candidateGroups == undefined || $scope.assignment.candidateGroups.length == 0)
    {
    	$scope.assignment.candidateGroups = [{value: '', name: ''}];
    }
    
    var groupValueIndex = 1;
    $scope.addCandidateGroupValue = function(index) {
        $scope.assignment.candidateGroups.splice(index + 1, 0, {value: '', name: ''});
    };

    // Click handler for - button after enum value
    $scope.removeCandidateGroupValue = function(index) {
        $scope.assignment.candidateGroups.splice(index, 1);
    };

    $scope.save = function() {

        $scope.property.value = {};
		handleAssignmentInput($scope);
		if (!$scope.assignment.assignee && $scope.assignment.assigneeName) {
			$scope.assignment.assignee = $scope.assignment.assigneeName
		}
		var data = $scope.assignment.candidateUsers
		var groupData = $scope.assignment.candidateGroups
		if (data) {
			for (var i = 0; i < data.length; i++) {
				if (data[i].value && !data[i].name) {
					data[i].value = ''
				}
			}
		}
		if (groupData) {
			for (var i = 0; i < groupData.length; i++) {
				if (groupData[i].value && !groupData[i].name) {
					groupData[i].value = ''
				}
			}
		}
		$scope.assignment.candidateUsers = data && data.length > 0 ? data.filter(item => item.name && item.value) : []
		$scope.assignment.candidateGroups = groupData && groupData.length > 0 ? groupData.filter(item => item.name && item.value) : []
		console.log('user---', $scope.assignment.candidateUsers)
		console.log('group---', $scope.assignment.candidateGroups)
        $scope.property.value.assignment = $scope.assignment;
        $scope.updatePropertyInModel($scope.property);
        $scope.close();
    };

    // Close button handler
    $scope.close = function() {
    	handleAssignmentInput($scope);
    	$scope.property.mode = 'read';
    	$scope.$hide();
    };
    
    var handleAssignmentInput = function($scope) {
    	if ($scope.assignment.candidateUsers)
    	{
	    	var emptyUsers = true;
	    	var toRemoveIndexes = [];
	        for (var i = 0; i < $scope.assignment.candidateUsers.length; i++)
	        {
	        	if ($scope.assignment.candidateUsers[i].value != '')
	        	{
	        		emptyUsers = false;
	        	}
	        	else
	        	{
	        		toRemoveIndexes[toRemoveIndexes.length] = i;
	        	}
	        }
	        
	        for (var i = 0; i < toRemoveIndexes.length; i++)
	        {
	        	$scope.assignment.candidateUsers.splice(toRemoveIndexes[i], 1);
	        }
	        
	        if (emptyUsers)
	        {
	        	$scope.assignment.candidateUsers = undefined;
	        }
    	}
        
    	if ($scope.assignment.candidateGroups)
    	{
	        var emptyGroups = true;
	        var toRemoveIndexes = [];
	        for (var i = 0; i < $scope.assignment.candidateGroups.length; i++)
	        {
	        	if ($scope.assignment.candidateGroups[i].value != '')
	        	{
	        		emptyGroups = false;
	        	}
	        	else
	        	{
	        		toRemoveIndexes[toRemoveIndexes.length] = i;
	        	}
	        }
	        
	        for (var i = 0; i < toRemoveIndexes.length; i++)
	        {
	        	$scope.assignment.candidateGroups.splice(toRemoveIndexes[i], 1);
	        }
	        
	        if (emptyGroups)
	        {
	        	$scope.assignment.candidateGroups = undefined;
	        }
    	}
    };
}];