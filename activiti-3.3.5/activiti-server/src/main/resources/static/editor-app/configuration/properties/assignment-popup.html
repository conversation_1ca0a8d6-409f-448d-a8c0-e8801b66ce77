<div class="modal" ng-controller="KisBpmAssignmentPopupCtrl">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" ng-click="close()">&times;</button>
                <h2 translate>PROPERTY.ASSIGNMENT.TITLE</h2>
            </div>
            <div class="modal-body">
            
            	<div class="row row-no-gutter">
            		<div class="form-group">  
            			<label for="assigneeField">{{'PROPERTY.ASSIGNMENT.ASSIGNEE' | translate}}</label>
                        <!-- <input type="text" id="assigneeField" class="form-control" ng-model="assignment.assignee" placeholder="{{'PROPERTY.ASSIGNMENT.ASSIGNEE_PLACEHOLDER' | translate}}" /> -->
                        
                        <select name="demoSelect" id="demoSelect" style="width: 100%; height: 34px;margin: -2px 0; border: 1px solid #ccc;" ng-model="assignment.assignee" ng-change="fu()">
                            <option
                                ng-if="assigneeList.length > 0"
                                ng-repeat="item in assigneeList"
                                value="{{item.id}}"
                                ng-selected="item.id == assignment.assignee"
                            >{{item.firstName}}</option>

                        </select>
                        <input 
                            type="text" 
                            name="inputSelect" 
                            id="inputSelect" 
                            style="position:relative;width: 96.5%;height: 30px;border: 0pt;top: -30px;outline: medium;left: 2px; border-right: solid 1px #ccc" 
                            ng-model="assignment.assigneeName" 
                            ng-change="fuInput()"
                            placeholder="{{'PROPERTY.ASSIGNMENT.ASSIGNEE_PLACEHOLDER' | translate}}"
                        />
            		</div>
            	</div>
            	
                <div class="row row-no-gutter" style="margin-Top: -25px">
                    <div class="form-group">
                    	<label for="userField">{{'PROPERTY.ASSIGNMENT.CANDIDATE_USERS' | translate}}</label>
                        <div ng-repeat="candidateUser in assignment.candidateUsers">
                            <!-- <input id="userField" class="form-control" type="text" ng-model="candidateUser.value" /> -->
                            <select name="userSelect" id="userSelect" style="width: 100%; height: 34px;margin: -2px 0; border: 1px solid #ccc;" ng-model="candidateUser.value" ng-change="fuCandidateUser($index)">
                                <option
                                    ng-if="assigneeList.length > 0"
                                    ng-repeat="item in assigneeList"
                                    value="{{item.id}}"
                                    ng-selected="item.id == candidateUser.value"
                                >{{item.firstName}}</option>
    
                            </select>
                            <input 
                                type="text" 
                                name="inputUsers" 
                                id="inputUsers" 
                                style="position:relative;width: 96.5%;height: 30px;border: 0pt;top: -30px;outline: medium;left: 2px; border-right: solid 1px #ccc" 
                                ng-model="candidateUser.name" 
                                ng-change="fuCandidateUserInput($index)"
                                placeholder="{{'PROPERTY.ASSIGNMENT.CANDIDATE_USERS_PLACEHOLDER' | translate}}"
                            />
                            <p style="margin-bottom: 0; font-size: 12px; position: relative; top: -24px;">
                                <i class="glyphicon glyphicon-minus clickable-property" ng-click="removeCandidateUserValue($index)"></i>
                                <i ng-if="$index == (assignment.candidateUsers.length - 1)" class="glyphicon glyphicon-plus clickable-property" ng-click="addCandidateUserValue($index)"></i>
                            </p>
                        </div>
                   	</div>
            
                    <div class="form-group" style="margin-Top: -25px">
                    	<label for="groupField">{{'PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS' | translate}}</label>
                        <div ng-repeat="candidateGroup in assignment.candidateGroups">
                              <!-- <input id="groupField" class="form-control" type="text" ng-model="candidateGroup.value" /> -->
                              <select name="groupSelect" id="groupSelect" style="width: 100%; height: 34px;margin: -2px 0; border: 1px solid #ccc;" ng-model="candidateGroup.value" ng-change="fuCandidateGroup($index)">
                                    <option
                                        ng-if="userGroup.length > 0"
                                        ng-repeat="item in userGroup"
                                        value="{{item.id}}"
                                        ng-selected="item.id == candidateGroup.value"
                                    >{{item.name}}</option>
        
                                </select>
                                <input 
                                    type="text" 
                                    name="inputGroup" 
                                    id="inputGroup" 
                                    style="position:relative;width: 96.5%;height: 30px;border: 0pt;top: -30px;outline: medium;left: 2px; border-right: solid 1px #ccc" 
                                    ng-model="candidateGroup.name" 
                                    ng-change="fuCandidatGroupInput($index)"
                                    placeholder="{{'PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS_PLACEHOLDER' | translate}}"
                                />
                                <p style="margin-bottom: 0; font-size: 12px; position: relative; top: -24px;">
                                    <i class="glyphicon glyphicon-minus clickable-property" ng-click="removeCandidateGroupValue($index)"></i>
                                    <i ng-if="$index == (assignment.candidateGroups.length - 1)" class="glyphicon glyphicon-plus clickable-property" ng-click="addCandidateGroupValue($index)"></i>
                                </p>
                        </div>
                    </div>
                </div>
            
            </div>
            <div class="modal-footer">
                <button ng-click="close()" class="btn btn-primary" translate>ACTION.CANCEL</button>
                <button ng-click="save()" class="btn btn-primary" translate>ACTION.SAVE</button>
            </div>
        </div>
    </div>
</div>