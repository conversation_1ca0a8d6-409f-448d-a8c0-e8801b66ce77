package test;

import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <p>Title: ActivitTest</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Slf4j
public class ActivitTest {
    @Autowired
    RepositoryService repositoryService;


    @Test
    @Transactional
    public void test1() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {

            Date endTime = sdf.parse("2019-05-15 18:20:30");
            //获取开始时间
            Date startTime = sdf.parse("2019-05-15 13:10:30");

            long timer = endTime.getTime() - startTime.getTime();

            long tasktime = timer / (1000 * 60);

            System.out.print(tasktime);

        } catch (Exception e) {

        }
    }

}
