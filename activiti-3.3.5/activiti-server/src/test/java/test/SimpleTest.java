package test;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <p>Title: SimpleTest</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2022/5/27</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Slf4j
public class SimpleTest {

    @Test
    public void test11() {
        Date parse = DateUtil.parse("2022-05-22");
        log.info("{}",parse);
        LocalDate localDate = LocalDateTime.ofInstant(parse.toInstant(),ZoneId.systemDefault()).toLocalDate();
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        //周日
        LocalDate week;
        if (dayOfWeek == DayOfWeek.SUNDAY) {
            week = localDate.minusDays(6);
        } else {
            week = localDate.minusDays(dayOfWeek.getValue() - 1);
        }
        Date result = Date.from(week.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
        log.info("{}",result);
    }
}









