-- AGV调度表测试数据
-- 用于测试 statMaterialBox 功能

INSERT INTO hv_wms_agv_scheduling (
    task_no, task_type, request_task_no, agv_no, agv_name, agv_type_no, agv_type_name,
    start_point, end_point, priority, scheduling_state, order_no, pallet_type_no, 
    pallet_type_name, pallet_no, pallet_name, manual, planned_start_time, planned_end_time,
    actual_start_time, actual_end_time, create_time, creator_name, site_num, 
    update_time, updater_name
) VALUES 
-- 1. 空框请求任务 - 已完成
(
    'TASK001', 0, 'REQ001', 'AGV001', 'AGV小车01', 'TYPE001', '标准AGV',
    'A01-01-01', 'B02-03-02', '2', 3, 'WO2024001', 1,
    '标准托盘', 'PALLET001', '标准托盘001', 0, '2024-01-15 08:00:00', '2024-01-15 08:30:00',
    '2024-01-15 08:05:00', '2024-01-15 08:25:00', '2024-01-15 07:55:00.000000', '系统管理员', 'SITE001',
    '2024-01-15 08:25:00.000000', '系统管理员'
),

-- 2. 满框调度任务 - 进行中
(
    'TASK002', 1, 'REQ002', 'AGV002', 'AGV小车02', 'TYPE001', '标准AGV',
    'C03-02-01', 'D01-01-03', '3', 2, 'WO2024002', 1,
    '标准托盘', 'PALLET002', '标准托盘002', 0, '2024-01-15 09:00:00', '2024-01-15 09:45:00',
    '2024-01-15 09:02:00', NULL, '2024-01-15 08:58:00.000000', '操作员001', 'SITE001',
    '2024-01-15 09:15:00.000000', '系统管理员'
),

-- 3. 配送任务 - 待开始
(
    'TASK003', 2, 'REQ003', 'AGV003', 'AGV小车03', 'TYPE002', '重载AGV',
    'E02-01-02', 'F03-02-01', '1', 0, 'WO2024003', 2,
    '重载托盘', 'PALLET003', '重载托盘003', 0, '2024-01-15 10:00:00', '2024-01-15 11:00:00',
    NULL, NULL, '2024-01-15 09:45:00.000000', '调度员001', 'SITE001',
    '2024-01-15 09:45:00.000000', '调度员001'
),

-- 4. 空框回收任务 - 任务开始
(
    'TASK004', 3, 'REQ004', 'AGV001', 'AGV小车01', 'TYPE001', '标准AGV',
    'G01-03-01', 'A01-01-02', '2', 1, 'WO2024004', 1,
    '标准托盘', 'PALLET004', '标准托盘004', 0, '2024-01-15 11:30:00', '2024-01-15 12:15:00',
    '2024-01-15 11:32:00', NULL, '2024-01-15 11:25:00.000000', '系统管理员', 'SITE001',
    '2024-01-15 11:32:00.000000', '系统管理员'
),

-- 5. 手动调度任务 - 已完成
(
    'TASK005', 4, 'REQ005', 'AGV004', 'AGV小车04', 'TYPE002', '重载AGV',
    'H02-01-01', 'I01-02-03', '5', 3, 'WO2024005', 2,
    '重载托盘', 'PALLET005', '重载托盘005', 1, '2024-01-15 13:00:00', '2024-01-15 14:00:00',
    '2024-01-15 13:05:00', '2024-01-15 13:55:00', '2024-01-15 12:55:00.000000', '手动操作员', 'SITE001',
    '2024-01-15 13:55:00.000000', '手动操作员'
);

-- 查询验证数据
SELECT 
    task_no,
    task_type,
    CASE task_type 
        WHEN 0 THEN '空框请求'
        WHEN 1 THEN '满框调度'
        WHEN 2 THEN '配送任务'
        WHEN 3 THEN '空框回收'
        WHEN 4 THEN '手动调度'
    END as task_type_name,
    agv_no,
    agv_name,
    start_point,
    end_point,
    CASE scheduling_state
        WHEN 0 THEN '待开始'
        WHEN 1 THEN '任务开始'
        WHEN 2 THEN '走出储位'
        WHEN 3 THEN '任务完成'
    END as scheduling_state_name,
    order_no,
    pallet_no,
    CASE manual
        WHEN 0 THEN '自动'
        WHEN 1 THEN '手动'
    END as manual_type,
    create_time
FROM hv_wms_agv_scheduling 
ORDER BY create_time DESC;
