package com.hvisions.hiperbase.transportTool;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "运输工具类型查询条件")
public class TransportToolTypeQueryDTO extends PageInfo {
    /**
     * 运输工具类型编码 (PK)
     */
    @ApiModelProperty(value = "运输工具类型编号")
    private String transportToolTypeCode;

    /**
     * 运输工具类型描述
     */
    @ApiModelProperty(value = "运输工具类型描述")
    private String transportToolTypeDesc;

}
