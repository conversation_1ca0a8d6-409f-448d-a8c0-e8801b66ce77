package com.hvisions.hiperbase.importTemplate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P> 料点配置导入模版  <P>
 *
 * <AUTHOR>
 * @date 2025/1/21
 */
@Data
public class MaterialPointTemplate {

    /**
     * 料点配置编码
     */
    @ApiModelProperty(value = "料点配置编码")
    private String pointCode;


    /**
     * 料点配置名称
     */
    @ApiModelProperty(value = "料点配置名称")
    private String pointName;


    /**
     * 料点区域编码
     */
    @ApiModelProperty(value = "料点区域编码")
    private String pointAreaCode;

}
