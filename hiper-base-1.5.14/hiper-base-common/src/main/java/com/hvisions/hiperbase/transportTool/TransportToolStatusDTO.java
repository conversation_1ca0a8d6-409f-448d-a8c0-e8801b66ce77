package com.hvisions.hiperbase.transportTool;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.excel.LocalDateTimeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
@ApiModel(description = "运输工具")
public class TransportToolStatusDTO {

    /**
     * 主键
     */
    @ExcelIgnore
    protected Integer id;

    /**
     * 运输工具编码 (PK)
     */
    @NotBlank(message = "运输工具编号不能为空")
    @ApiModelProperty(value = "运输工具编号")
    @ExcelProperty(value = "运输工具编码")
    @Column(updatable = false)
    private String transportToolCode;

    /**
     * 运输工具类型名称
     */
    @ExcelProperty(value = "运输工具类型")
    @ApiModelProperty(value = "运输工具类型")
    private String transportToolTypeCode;

    /**
     * 工作状态
     */
    @ApiModelProperty(value = "工作状态")
    @ExcelProperty(value = "工作状态")
    private Integer workStatus;

    /**
     * 当前位置
     */
    @ApiModelProperty(value = "当前位置")
    @ExcelProperty(value = "当前位置")
    private String currentLocation;

    /**
     * IP地址
     */
    @ApiModelProperty(value = "IP地址")
    @ExcelProperty(value = "IP地址")
    private String ipAddress;

    /**
     * 电量
     */
    @ApiModelProperty(value = "电量")
    @ExcelProperty(value = "电量")
    private String battery;


    /**
     * 创建时间
     */
    @Column(updatable = false)
    @ExcelProperty(value = "创建时间",converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ExcelProperty(value = "修改时间",converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    protected String creatorId;

    /**
     * 修改人
     */
    @ExcelProperty(value = "更新人")
    protected String updaterId;

    /**
     * 用于后续saas服务租户字段
     */
    @ExcelIgnore
    protected String siteNum;

}
