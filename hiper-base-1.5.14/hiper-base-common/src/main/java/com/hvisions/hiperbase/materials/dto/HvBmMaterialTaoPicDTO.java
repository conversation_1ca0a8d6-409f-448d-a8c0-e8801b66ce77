package com.hvisions.hiperbase.materials.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.excel.LocalDateConverter;
import com.hvisions.common.excel.LocalDateTimeConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-04-16 9:34
 */
@Data
public class HvBmMaterialTaoPicDTO {
    /**
     * 主键ID
     */
    @ExcelIgnore
    private long ID;
    /**
     * 任务号
     */
    @ExcelProperty(value = "任务号")
    private String taskCode;
    /**
     * 产线号
     */
    @ExcelProperty(value = "产线号")
    private String lineCode;
    /**
     * 工单号
     */
    @ExcelProperty(value = "工单号")
    private String workOrder;
    /**
     * 船型
     */
    @ExcelProperty(value = "船型")
    private String shipModel;
    /**
     * 分段
     */
    @ExcelProperty(value = "分段")
    private String segmentationCode;
    /**
     * 套料图编号
     */
    @ExcelProperty(value = "套料图编号")
    private String taoFileCode;
    /**
     * 原料规格
     */
    @ExcelProperty(value = "原料规格")
    private String specifications;
    /**
     * 长度
     */
    @ExcelProperty(value = "长度")
    private String length;
    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private String quality;
    /**
     * 重量
     */
    @ExcelProperty(value = "重量")
    private String weight;
    /**
     * 计划结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "计划结束时间",converter = LocalDateTimeConverter.class)
    private LocalDateTime planEndTime;
    /**
     * 下发操作人
     */
    @ExcelProperty(value = "下发操作人")
    @ExcelIgnore
    private Integer sendUserId;
    /**
     * 下发时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "下发时间",converter = LocalDateTimeConverter.class)
    @ExcelIgnore
    private LocalDateTime sendTime;

    @ExcelIgnore
    private String planEndTimeMin;
    @ExcelIgnore
    private String planEndTimeMax;
}
