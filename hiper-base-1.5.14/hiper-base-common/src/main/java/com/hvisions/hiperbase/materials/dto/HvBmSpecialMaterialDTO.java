package com.hvisions.hiperbase.materials.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-04-17 9:28
 */
@Data
public class HvBmSpecialMaterialDTO {
    /**
     * 主键ID
     */
    @ExcelIgnore
    private long id;
    /**
     * 物料ID
     */
    @ExcelIgnore
    private Integer materialId;

    /**
     * 物料编号(导入显示用)
     */
    @ExcelProperty(value = "物料编号")
    @ExcelIgnore
    private String materialCode;

    /***
     * 特征值
     */
    @ExcelProperty(value = "特征值")
    private String eigenvalue;

    /**
     * 最小宽度
     */
    @ExcelProperty(value = "最小宽度")
    private Double minWidth;
    /**
     * 最大宽度
     */
    @ExcelProperty(value = "最大宽度")
    private Double maxWidth;
    /**
     * 最小长度
     */
    @ExcelProperty(value = "最小长度")
    private Double minLength;
    /**
     * 最大长度
     */
    @ExcelProperty(value = "最大长度")
    private Double maxLength;
    /**
     * 产线ID
     */
    @ExcelIgnore
    private Integer lineId;

    /**
     * 产线编号
     */
    @ExcelProperty(value = "产线编号")
    private String lineCode;

    /**
     * 料点区域ID
     */
    @ExcelIgnore
    private Integer pointAreaId;

    /**
     * 料点区域编号(导入显示用)
     */
    @ExcelProperty(value = "料点区域编号")
    private String pointAreaCode;

    /**
     * 创建者
     */
    @ExcelIgnore
    private Integer creatorId;
    /**
     * 创建时间
     */
    @ExcelIgnore
    private LocalDateTime createTime;
    /**
     * 修改者
     */
    @ExcelIgnore
    private Integer updaterId;
    /**
     * 修改时间
     */
    @ExcelIgnore
    private LocalDateTime updateTime;
}
