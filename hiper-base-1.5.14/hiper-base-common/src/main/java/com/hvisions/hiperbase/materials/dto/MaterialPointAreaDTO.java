package com.hvisions.hiperbase.materials.dto;

import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.interfaces.IObjectType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class MaterialPointAreaDTO extends PageInfo implements IObjectType {

    @ApiModelProperty(value = "料点区域编码")
    private Integer id;

    @NotBlank(message = "料点区域编码不能为空")
    @ApiModelProperty(value = "料点区域编码")
    private String pointAreaCode;

    @NotBlank(message = "料点区域名称不能为空")
    @ApiModelProperty(value = "料点区域名称")
    private String pointAreaName;

    @ApiModelProperty(value = "工位id")
    private Integer locationId;

    /**
     * 工位名
     */
    private String locationName;
    
    @Override
    public Integer getObjectType() {
        return null;
    }
}
