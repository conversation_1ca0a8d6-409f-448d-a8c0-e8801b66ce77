package com.hvisions.hiperbase.materials.classdto;

import com.hvisions.hiperbase.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>Title: MaterialClassProperty</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class MaterialClassPropertyDTO extends SysBaseDTO {

    /**
     * 属性编码
     */
    @NotBlank(message = "物料属性编码不能为空")
    @Length(max = 200)
    @ApiModelProperty(value = "属性编码")
    private String code;

    /**
     * 属性名
     */
    @NotBlank(message = "物料属性名称不能为空")
    @Length(max = 200)
    @ApiModelProperty(value = "属性名称")
    private String name;
    /**
     * 属性数据类型
     */
    @ApiModelProperty(value = "属性数据类型", allowableValues = "1,2,3", notes = "1代表字符串，2代表长整型，3代表float类型")
    private Integer dateType;

    /**
     * 物料类型编码
     */
    @ApiModelProperty(value = "物料类型编码主键")
    private Integer classId;
    /**
     * 是否是常量
     */
    @NotNull(message = "是否是常量不能为空")
    @ApiModelProperty(value = "是否是常量")
    private Boolean isConst;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private String value;
}