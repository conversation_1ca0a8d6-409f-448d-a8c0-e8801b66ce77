package com.hvisions.hiperbase.materials.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MaterialPointQueryDTO extends PageInfo {

    /**
     * 料点配置编号
     */
    @ApiModelProperty(value = "料点分组编码")
    private String pointCode;

    /**
     * 料点配置名称
     */
    @ApiModelProperty(value = "料点配置编码")
    private String pointName;

    /**
     * 料点区域id
     */
    @ApiModelProperty(value = "料点区域")
    private Integer pointAreaId;
}
