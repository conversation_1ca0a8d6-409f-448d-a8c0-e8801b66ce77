package com.hvisions.hiperbase.materials.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: MaterialTypeEnum</p >
 * <p>Description: 物料分组枚举类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum MaterialParseTypeEnum implements IKeyValueObject {
    /**
     * 固定位置
     */
    FIX_POSITION(1, "固定位置"),
    /**
     * 正则表达式
     */
    REGULAR(2, "正则表达式"),
    ;

    private Integer code;
    private String name;

    MaterialParseTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
