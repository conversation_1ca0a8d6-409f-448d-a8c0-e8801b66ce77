package com.hvisions.hiperbase.materials.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MaterialPointAreaQueryDTO extends PageInfo {

    /**
     * 料点配置编号
     */
    @ApiModelProperty(value = "料点区域编码")
    private String pointAreaCode;

    /**
     * 料点配置名称
     */
    @ApiModelProperty(value = "料点区域编码")
    private String pointAreaName;

    /**
     * location编号
     */
    @ApiModelProperty(value = "料点配置名称")
    private Integer locationId;
}
