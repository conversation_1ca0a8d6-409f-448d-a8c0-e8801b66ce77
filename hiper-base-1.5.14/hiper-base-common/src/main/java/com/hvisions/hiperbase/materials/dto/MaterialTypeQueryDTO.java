package com.hvisions.hiperbase.materials.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: MaterialTypeQueryDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-09-06</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class MaterialTypeQueryDTO extends PageInfo {

    /**
     * 物料类型名称（唯一且不可修改）
     */
    @ApiModelProperty(value = "物料类型名称(唯一且不可修改)")
    private String materialTypeName;

    /**
     * 物料类型编码
     */
    @ApiModelProperty(value = "物料类型编码")
    private String materialTypeCode;

    /**
     * 物料类型备注
     */
    @ApiModelProperty(value = "物料类型备注")
    private String materialTypeDesc;

    /**
     * 类型父级Id
     */
    @ApiModelProperty(value = "类型父级Id")
    private Integer parentId;
}