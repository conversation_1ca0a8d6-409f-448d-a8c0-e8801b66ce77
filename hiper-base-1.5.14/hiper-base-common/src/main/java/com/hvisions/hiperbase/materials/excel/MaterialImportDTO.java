package com.hvisions.hiperbase.materials.excel;

import com.hvisions.common.annotation.ExcelAnnotation;
import com.hvisions.common.interfaces.IExtendObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>Title: MaterialImportDTO</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2022/1/26</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@Getter
@Setter
@ToString
public class MaterialImportDTO implements IExtendObject {
    @ExcelAnnotation(ignore = true)
    private Integer id;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码", required = true, readOnly = true)
    String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String materialDesc;

    /***
     * 特征值
     */
    @ApiModelProperty(value = "特征值")
    private String eigenvalue;

    /**
     * 是否追溯
     */
    @ApiModelProperty(value = "是否追溯")
    private Boolean serialNumberProfile;

    /**
     * 物料类型编码
     */
    @ApiModelProperty(value = "物料类型编码")
    private String materialTypeCode;

    /**
     * 物料类型名称
     */
    @ApiModelProperty(value = "物料类型名称")
    private String materialTypeName;

    /**
     * 计量单位编码
     */
    @ApiModelProperty(value = "计量单位编码")
    private String uomName;

    /**
     * 物料分组编码
     */
    @ApiModelProperty(value = "物料分组编码")
    private String materialGroupCode;

    /**
     * 扩展属性
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(name = "扩展属性", value = "扩展属性")
    private Map<String, Object> extend = new HashMap<>();


    /**
     * 零件规格
     */
    @ApiModelProperty(value = "零件规格")
    private String specs;

    /**
     * 零件重量
     */
    @ApiModelProperty(value = "零件重量")
    private String weight;

    /**
     * 零件材质
     */
    @ApiModelProperty(value = "零件材质")
    private String quality;

    /**
     * 是否外发（0：是，1：否）
     */
    @ApiModelProperty(value = "外发(0：是，1：否)")
    private Integer outGoing;

    @ApiModelProperty(value = "长度")
    private String mWidth;

    @ApiModelProperty(value = "宽度")
    private String mLength;


    /**
     * 自制/外协（0：自制，1：外协）
     */
    @ApiModelProperty(value = "0：自制，1：外协")
    private Integer specialPurchaseTypeCode;
}









