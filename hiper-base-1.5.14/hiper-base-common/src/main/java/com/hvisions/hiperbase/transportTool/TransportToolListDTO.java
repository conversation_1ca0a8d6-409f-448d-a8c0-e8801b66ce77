package com.hvisions.hiperbase.transportTool;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.excel.LocalDateTimeConverter;
import com.hvisions.hiperbase.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
@ApiModel(description = "运输工具")
public class TransportToolListDTO {

    /**
     * 主键
     */
    @ExcelIgnore
    protected Integer id;


    /**
     * 运输工具编码 (PK)
     */
    @ApiModelProperty(value = "运输工具编号")
    private String transportToolCode;

    /**
     * 运输工具描述
     */
    @ApiModelProperty(value = "运输工具描述")
    private String transportToolDesc;

    /**
     * 运输工具类型
     */
    @ApiModelProperty(value = "运输工具类型编号")
    private String transportToolTypeCode;

    /**
     * 运输工具类型描述
     */
    @ApiModelProperty(value = "运输工具类型描述")
    private String transportToolTypeDesc;

    /**
     * 运输工具规格
     */
    @ApiModelProperty(value = "运输工具规格")
    private String transportToolSpecs;

    /**
     * 是否自动设备
     */
    @ApiModelProperty(value = "是否自动设备 0否/1是")
    private Integer isAutoDevice;


    /**
     * 创建时间
     */
    @Column(updatable = false)
    @ExcelProperty(value = "创建时间",converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ExcelProperty(value = "修改时间",converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    protected String creatorId;

    /**
     * 修改人
     */
    @ExcelProperty(value = "更新人")
    protected String updaterId;

    /**
     * 用于后续saas服务租户字段
     */
    @ExcelIgnore
    protected String siteNum;

}
