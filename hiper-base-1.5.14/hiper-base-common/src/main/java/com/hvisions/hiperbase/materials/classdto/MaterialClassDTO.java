package com.hvisions.hiperbase.materials.classdto;

import com.hvisions.hiperbase.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <p>Title: MaterialClassDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class MaterialClassDTO extends SysBaseDTO {

    /**
     * 编码
     */
    @NotBlank(message = "物料属性类型编码不能为空")
    @Length(max = 200)
    @ApiModelProperty(value = "编码", required = true)
    private String code;
    /**
     * 名称
     */
    @NotBlank(message = "物料属性类型名称不能为空")
    @Length(max = 200)
    @ApiModelProperty(value = "名称", required = true)
    private String name;
}