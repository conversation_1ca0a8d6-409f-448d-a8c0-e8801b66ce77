package com.hvisions.hiperbase.transportTool;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "运输工具查询条件")
public class TransportToolQueryDTO extends PageInfo {
    /**
     * 运输工具编码 (PK)
     */
    @ApiModelProperty(value = "运输工具编号")
    private String transportToolCode;

    /**
     * 运输工具描述
     */
    @ApiModelProperty(value = "运输工具描述")
    private String transportToolDesc;

    /**
     * 运输工具类型
     */
    @ApiModelProperty(value = "运输工具类型")
    private String transportToolTypeCode;

    /**
     * 运输工具规格
     */
    @ApiModelProperty(value = "运输工具规格")
    private String transportToolSpecs;

    /**
     * 是否自动设备
     */
    @ApiModelProperty(value = "是否自动设备")
    private Integer isAutoDevice;

}
