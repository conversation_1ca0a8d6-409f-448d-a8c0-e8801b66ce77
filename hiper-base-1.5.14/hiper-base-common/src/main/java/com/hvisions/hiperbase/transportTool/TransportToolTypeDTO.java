package com.hvisions.hiperbase.transportTool;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.annotation.ExcelAnnotation;
import com.hvisions.common.excel.LocalDateTimeConverter;
import com.hvisions.hiperbase.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@ApiModel(description = "运输工具类型")
public class TransportToolTypeDTO {
    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Integer id;

    /**
     * 运输工具类型编码 (PK)
     */

    @NotBlank(message = "运输工具类型编号不能为空")
    @ApiModelProperty(value = "运输工具类型编号")
    private String transportToolTypeCode;

    /**
     * 运输工具类型描述
     */
    @NotBlank(message = "运输工具类型描述不能为空")
    @ApiModelProperty(value = "运输工具类型描述")
    private String transportToolTypeDesc;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    protected String creatorId;

    /**
     * 修改人
     */
    @ExcelProperty(value = "更新人")
    protected String updaterId;

    /**
     * 创建时间
     */
    @Column(updatable = false)
    @CreatedDate
    @ExcelProperty(value = "创建时间",converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected LocalDateTime createTime;

    /**
     * 修改时间
     */
    @LastModifiedDate
    @ExcelProperty(value = "修改时间",converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected LocalDateTime updateTime;

    /**
     * 系统代码
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "系统代码", readOnly = true)
    protected String siteNum;

}
