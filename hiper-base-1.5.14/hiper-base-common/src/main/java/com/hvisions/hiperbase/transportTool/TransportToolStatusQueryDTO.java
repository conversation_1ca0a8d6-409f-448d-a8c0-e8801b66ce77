package com.hvisions.hiperbase.transportTool;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

@Data
@ApiModel(description = "运输工具查询条件")
public class TransportToolStatusQueryDTO extends PageInfo {
    /**
     * 运输工具编码 (PK)
     */
    @ApiModelProperty(value = "运输工具编号")
    private String transportToolCode;

    /**
     * 运输工具类型
     */
    @ApiModelProperty(value = "运输工具类型")
    private String transportToolTypeCode;

    /**
     * 工作状态
     */
    @ApiModelProperty(value = "工作状态")
    private Integer workStatus;

    /**
     * 当前位置
     */
    @ApiModelProperty(value = "当前位置")
    private String currentLocation;

    /**
     * IP地址
     */
    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    /**
     * 电量
     */
    @ApiModelProperty(value = "电量")
    private String battery;


}
