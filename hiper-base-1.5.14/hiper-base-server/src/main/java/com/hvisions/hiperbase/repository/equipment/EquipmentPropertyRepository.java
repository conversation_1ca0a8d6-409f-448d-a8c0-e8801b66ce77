package com.hvisions.hiperbase.repository.equipment;

import com.hvisions.hiperbase.entity.equipment.HvBmEquipmentProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: EquipmentPropertyRepository</p>
 * <p>Description: 设备属性仓储</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface EquipmentPropertyRepository extends JpaRepository<HvBmEquipmentProperty, Integer> {
    /**
     * 根据编码和属性类型编码查询
     *
     * @param code      属性编码
     * @param classCode 类型编码
     * @return 属性列表
     */
    @Query(value = "select h from HvBmEquipmentProperty h where h.code =?1 and h.classCode = ?2")
    List<HvBmEquipmentProperty> findAllByCodeAndClassCode(String code, String classCode);

    /**
     * 根据设备id和属性类型编码查询属性
     *
     * @param equipmentId 设备id
     * @param code        属性编码
     * @return 属性列表
     */
    HvBmEquipmentProperty findByEquipmentIdAndCode(Integer equipmentId, String code);

    /**
     * 根据设备id查询属性
     *
     * @param equipmentId 设备id
     * @return 设备属性列表
     */
    List<HvBmEquipmentProperty> findByEquipmentId(Integer equipmentId);

    /**
     * 根据设备id删除设备属性
     *
     * @param equipmentId 设备id
     */
    void deleteAllByEquipmentId(Integer equipmentId);
}

    
    
    
    