package com.hvisions.pms.parameterdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: OperationParameterPhoneDTO</p >
 * <p>Description: 手机端录入工序参数DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/14</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OperationParameterPhoneDTO {


    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private Integer operationId;

    /**
     * 任务Id
     */
    @ApiModelProperty(value = "任务Id")
    private Integer taskId;

    /**
     * 录入工序参数实际值DTO
     */
    private List<OperationParameterInsertDTO> operationParameterInsertDTOS;

}
