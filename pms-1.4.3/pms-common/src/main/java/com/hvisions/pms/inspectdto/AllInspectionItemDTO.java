package com.hvisions.pms.inspectdto;

import com.hvisions.pms.changeshiftsdto.BuildWorkCenterDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: AllInspectionItemDTO</p >
 * <p>Description: 检查项目所有信息</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-14</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class AllInspectionItemDTO extends InspectionItemDTO {

    /**
     * 检查点
     */
    @ApiModelProperty(value = "检查点列表")
    private List<CheckPointDTO> checkPointDTOS;

    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备Id")
    private List<BuildWorkCenterDTO> buildWorkCenterDTOList;
}