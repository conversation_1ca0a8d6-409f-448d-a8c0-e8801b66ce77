package com.hvisions.pms.changeshiftsdto;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: InspectDTO</p >
 * <p>Description: 交班时岗位检查记录</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InspectDTO extends SysBaseDTO {

    /**
     * 检查点名称
     */
    @ApiModelProperty(value = "检查点名称")
    private String checkPointName;

    /**
     * 检查结果
     */
    @ApiModelProperty(value = "检查结果")
    private boolean result;

    /**
     * 交接班记录ID
     */
    @ApiModelProperty(value = "交接班记录ID")
    private Integer changeShiftsId;

}