package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
@Data
public class PmPolishPlanTemplate {


    /**
     * 任务编号
     */
    @ExcelProperty("任务编号")
    @ApiModelProperty(value = "任务编号")
    private String code;


    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String work_order_code;

    /**
     * 任务名称
     */
    @ExcelProperty("任务名称")
    @ApiModelProperty(value = "任务名称")
    private String name;



    /**
     * 料框编号
     */
    @ExcelProperty("料框编号")
    @ApiModelProperty(value = "料框编号")
    private String pod_code;


    /**
     * 线体编号
     */
    @ExcelProperty("产线编号")
    @ApiModelProperty(value = "产线编号")
    private String line_code;

    /**
     * 作业编号
     */
    @ExcelProperty("船号")
    @ApiModelProperty(value = "船号")
    private String ship_number;

    /**
     * 分段号
     */
    @ExcelProperty("分段号")
    @ApiModelProperty(value = "分段号")
    private String segmentation_code;

    /**
     * 工位编号
     */
    @ExcelProperty("工位编号")
    @ApiModelProperty(value = "工位编号")
    private String station_type;

    /**
     * 工位名称
     */
    @ExcelProperty("工位名称")
    @ApiModelProperty(value = "工位名称")
    private String station_name;

    /**
     * 加工数量
     */
    @ExcelProperty("加工数量")
    @ApiModelProperty(value = "加工数量")
    private Integer qty;

    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    private String type;

    /**
     * 班次
     */
    @ExcelProperty("班次")
    @ApiModelProperty(value = "班次")
    private String batch_no;

}
