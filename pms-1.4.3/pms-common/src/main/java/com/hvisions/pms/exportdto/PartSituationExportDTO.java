package com.hvisions.pms.exportdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <P> 零件情况导出   <P>
 *
 * <AUTHOR>
 * @date 2025/1/9
 */
@Data
public class PartSituationExportDTO {

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipNo;

    /**
     * 船型
     */
    @ApiModelProperty(value = "船型")
    private String shipModel;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;



    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 所需数量
     */
    @ApiModelProperty(value = "所需数量")
    private BigDecimal needQuantity;


    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQuantity;

    /**
     * 已使用数量
     */
    @ApiModelProperty(value = "已使用数量")
    private BigDecimal useQuantity;

    /**
     * 缺少数量
     */
    @ApiModelProperty(value = "缺少数量")
    private BigDecimal lackQuantity;
}
