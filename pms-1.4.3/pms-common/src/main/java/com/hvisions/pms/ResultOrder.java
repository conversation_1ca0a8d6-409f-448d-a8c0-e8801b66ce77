package com.hvisions.pms;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hvisions.common.enums.ResultEnum;
import lombok.Data;

/**
 * <p>Title: ResultVO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-11-05</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class ResultOrder<T> extends com.hvisions.common.vo.ResultVO {
    public static final int CHOOSE = 200;
    /**
     * 返回数据
     */
    private T data;
    /**
     * 结果代码
     */
    private Integer code;
    /**
     * 结果消息
     */
    private String message;

    @JsonIgnore
    public boolean isSuccess() {
        return code == SUCCESS;
    }


    private ResultOrder(ResultEnum result, String message, T data) {
        this.message = message;
        this.code = result.getCode();
        this.data = data;
    }

    public ResultOrder(String message, int code, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    private void setMessage(String message) {
        this.message = message;
    }

    public static <T> ResultOrder<T> success(String message, T o) {
        return new ResultOrder<T>(message, CHOOSE, o);
    }

    @Override
    public String toString() {
        return "ResultVO{" +
                "data=" + data +
                ", code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}