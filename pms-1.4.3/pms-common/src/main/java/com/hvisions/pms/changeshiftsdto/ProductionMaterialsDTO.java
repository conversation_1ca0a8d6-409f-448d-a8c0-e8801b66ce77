package com.hvisions.pms.changeshiftsdto;

import com.hvisions.pms.dto.OperationOutPutMaterialDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: ProductionMaterialsDTO</p >
 * <p>Description: 交班时产出物料信息</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductionMaterialsDTO extends OperationOutPutMaterialDTO {


    /**
     * 交接班记录ID
     */
    @ApiModelProperty(value = "交接班记录ID")
    private Integer changeShiftsId;

    /**
     * 工单ID
     */
    @ApiModelProperty(value = "工单ID")
    private Integer orderId;

}