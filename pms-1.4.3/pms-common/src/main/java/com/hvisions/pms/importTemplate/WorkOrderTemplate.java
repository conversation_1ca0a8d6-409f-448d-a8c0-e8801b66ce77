package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-04-09 16:07
 */
@Data
public class WorkOrderTemplate {

    /**
     * 工单计划编码
     */
    @ExcelProperty("批次号")
    @ApiModelProperty(value = "批次号", required = true)
    private String planCode;

    /**
     * 工单编码
     */
    @ExcelProperty("工单编码")
    @ApiModelProperty(value = "工单编码", required = true)
    private String workOrderCode;


    /**
     * 物料编码
     */
    @ExcelProperty("物料编码")
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ExcelProperty("物料名称")
    @ApiModelProperty(value = "物料名称")
    private String materialName;


    /**
     * 父物料号
     */
    @ExcelProperty("父物料号(为零件工单时必须)")
    @ApiModelProperty(value = "父物料号(为零件工单时必须)")
    private String parentMaterialCode;

    /**
     * 父工单号
     */
    @ExcelProperty("父工单号")
    @ApiModelProperty(value = "父工单号")
    private String parentWorkOrderCode;

    /**
     * 工单类型
     */
    @ExcelProperty("工单类型")
    @ApiModelProperty(value = "工单类型(0：正常、1：返修)")
    private String orderType;


    /**
     * 计划开始时间
     */
    @ExcelProperty("计划开始时间")
    @ApiModelProperty(value = "计划开始时间")
    private String planStartTime;

    /**
     * 计划结束时间
     */
    @ExcelProperty("计划结束时间")
    @ApiModelProperty(value = "计划结束时间")
    private String  planEndTime;

    /**
     * 是否型材计划(1 是 0 否)
     */
    @ExcelProperty("是否型材计划(1 是 0 否)")
    @ApiModelProperty(value = "是否型材计划(1 是 0 否)")
    private String  xc;

    /**
     * 是否型材计划(1 是 0 否)
     */
    @ExcelProperty("是否型材计划(1 是 0 否)")
    @ApiModelProperty(value = "是否型材计划(1 是 0 否)")
    private String quantity;


    /**
     * 车间代码
     */
    @ExcelProperty("车间代码")
    @ApiModelProperty(value = "车间代码", required = true)
    private String areaCode;

    /**
     * 车间名称
     */
    @ExcelProperty("车间名称")
    @ApiModelProperty(value = "车间名称", required = true)
    private String areaName;

    /**
     * 船号
     */
    @ExcelProperty("船号")
    @ApiModelProperty(value = "船号")
    private String shipNo;

    /**
     * 船型
     */
    @ExcelProperty("船型")
    @ApiModelProperty(value = "船型")
    private String ShipModel;

    /**
     * 分段号
     */
    @ExcelProperty("分段号")
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;

    /**
     * 执行顺序
     */
    @ExcelProperty("执行顺序")
    @ApiModelProperty(value = "执行顺序")
    private String executeSequence;

    /**
     * 工艺路线编码
     */
    @ExcelProperty("工艺路线编码")
    @ApiModelProperty(value = "工艺路线编码")
    private String routeCode;


    /**
     * 工艺路线版本
     */
    @ExcelProperty("工艺路线版本")
    @ApiModelProperty(value = "工艺路线版本")
    private String routeVersion;

    /**
     * bom编号
     */
    @ExcelProperty("bom编号")
    @ApiModelProperty(value = "bom编号")
    private String bomCode;

    /**
     * 组立bom版本
     */
    @ExcelProperty("组立bom版本")
    @ApiModelProperty(value = "组立bom版本")
    private String  bomVersion;

    /**
     * 是否外发
     */
    @ExcelProperty("是否外发 0 否 1 是  （零件工单必填）")
    @ApiModelProperty(value = "是否外发 0 否 1 是  （零件工单必填）")
    private String outFlag;

    /**
     * 是否外发
     */
    @ExcelProperty("自制/外协 2 是自制 1是外协（零件工单必填）")
    @ApiModelProperty(value = "自制/外协 2 是自制 1是外协（零件工单必填）")
    private String specialPurchaseTypeCode;

    /**
     * 是否外发
     */
    @ExcelProperty("流向代码 A1,A2,C1,C2,C3,C4………………")
    @ApiModelProperty(value = "流向代码 A1,A2,C1,C2,C3,C4………………")
    private String  blockCode;


    /**
     * 班次编号/班次名称
     */
    @ExcelProperty("班次编号")
    @ApiModelProperty(value = "班次编号")
    private String shift;

    /**
     * 班组编号/班组名称
     */
    @ExcelProperty("班组编号")
    @ApiModelProperty(value = "班组编号")
    private String crew;


    @ExcelProperty("组立：0/零件：1 工单")
    @ApiModelProperty(value = "组立/零件工单")
    private Integer usedType;
}
