package com.hvisions.pms.exportdto;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P> gen文件信息导出DTO   <P>
 *
 * <AUTHOR>
 * @date 2024/12/24
 */
@Data
public class XcMaterialCutPlanDetail0ExportDTO {
    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Long id;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private Long orderId;

    /**
     * 作业编号
     */
    @ApiModelProperty(value = "作业编号")
    private String orderNo;

    /**
     * 切割计划子编号
     */
    @ApiModelProperty(value = "切割计划子编号")
    private String subPlanNo;

    /**
     * 零件数量
     */
    @ApiModelProperty(value = "零件数量")
    private String qty;

    /**
     * GEN文件地址
     */
    @ApiModelProperty(value = "GEN文件地址")
    private String genFilePath;


    /**
     * 型材原料物料编码
     */
    @ApiModelProperty(value = "型材原料物料编码")
    private String materialCode;

    /**
     * 废料总长度（型材-零件-余料=废料）
     */
    @ApiModelProperty(value = "废料总长度（型材-零件-余料=废料）")
    private String scrapLength;

    /**
     * 型材利用率
     */
    @ApiModelProperty(value = "型材利用率")
    private String plateUtilRate;

    /**
     * 订单满足率
     */
    @ApiModelProperty(value = "订单满足率")
    private String orderFulfillRate;

    /**
     * 余料率
     */
    @ApiModelProperty(value = "余料率")
    private String scrapRate;

    /**
     * 报废率
     */
    @ApiModelProperty(value = "报废率")
    private String wasteRate;

    /**
     *型材总长度
     */
    @ApiModelProperty(value = "型材总长度")
    private String totalPlateLength;

    /**
     * 零件总长度
     */
    @ApiModelProperty(value = "零件总长度")
    private String totalPartLength;

    /**
     * 余料总长度
     */
    @ApiModelProperty(value = "余料总长度")
    private String totalScrapLength;
}
