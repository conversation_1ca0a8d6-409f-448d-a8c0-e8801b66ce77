package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/4/24 14:41
 */

@Data
public class MaterialCutPlanTemplate {
    /**
     * 切割计划编码
     */
    @ExcelProperty("切割计划编码")
    @ApiModelProperty(value = "切割计划编码",required = true)
    private String cutPlanCode;

    /**
     * 套料批次
     */
    @ExcelProperty("套料批次")
    @ApiModelProperty(value = "套料批次",required = true)
    private String taoBatchCode;

    /**
     * 钢板原材料编码
     */
    @ExcelProperty("钢板原材料编码")
    @ApiModelProperty(value = "钢板原材料编码",required = true)
    private String materialType;

    /**
     * 数量
     */
    @ExcelProperty("数量")
    @ApiModelProperty(value = "数量",required = true)
    private Integer quality;

    /**
     * 长度
     */
    @ExcelProperty("长度")
    @ApiModelProperty(value = "长度",required = true)
    private String length;

    /**
     * 宽度
     */
    @ExcelProperty("宽度")
    @ApiModelProperty(value = "宽度",required = true)
    private String width;

    /**
     * 厚度
     */
    @ExcelProperty("厚度")
    @ApiModelProperty(value = "厚度",required = true)
    private String thickness;


    /**
     * 船号
     */
    @ExcelProperty("船号")
    @ApiModelProperty(value = "船号",required = true)
    private String shipNumber;


    /**
     * 船号
     */
    @ExcelProperty("分段号")
    @ApiModelProperty(value = "分段号",required = true)
    private String segmentationCode;

    /**
     * 批次
     */
//    @ExcelProperty("批次")
//    @ApiModelProperty(value = "批次",required = true)
//    private String batch;

    /**
     * 余料(0:是,1:否)
     */
    @ExcelProperty("余料(0:是,1:否)")
    @ApiModelProperty(value = "余料(0:是,1:否)",required = true)
    private Integer surplusMaterialFlag;

    /**
     * 切割数量
     */
    @ExcelProperty("切割数量")
    @ApiModelProperty(value = "切割数量",required = true)
    private Integer cutQuality;

    /**
     * 套料程序文件地址
     */
    @ExcelProperty("套料程序文件地址dxf")
    @ApiModelProperty(value = "套料程序文件地址dxf",required = true)
    private String taoFilePath;

    /**
     * 喷码程序文件地址
     */
    @ExcelProperty("喷码程序文件地址gen")
    @ApiModelProperty(value = "喷码程序文件地址gen",required = true)
    private String markingFilePath;

    /**
     * 切割程序文件地址
     */
    @ExcelProperty("切割程序文件地址cnc")
    @ApiModelProperty(value = "切割程序文件地址cnc",required = true)
    private String cutFilePath;

    /**
     * 套料PDF文件地址pdf
     */
    @ExcelProperty("套料PDF文件地址pdf")
    @ApiModelProperty(value = "套料PDF文件地址pdf",required = true)
    private String pdfFilePath;

    /**
     * 产线编码
     */
    @ExcelProperty("产线编码")
    @ApiModelProperty(value = "产线编码",required = true)
    private String lineCode;

    /**
     * 计划结束时间
     */
    @ExcelProperty("计划结束时间")
    @ApiModelProperty(value = "计划结束时间",required = true)
    private Date planEndTime;

    /**
     * 顺序
     */
    @ExcelProperty("顺序")
    @ApiModelProperty(value = "顺序",required = true)
    private Integer sequence;


    /**
     * 废料总重量（钢板-零件-余料=废料）
     */
    @ExcelProperty("废料总重量（钢板-零件-余料=废料）")
    @ApiModelProperty(value = "废料总重量（钢板-零件-余料=废料）",required = true)
    private String scrapWeight;

    /**
     * 切割效率
     */
    @ExcelProperty("切割效率")
    @ApiModelProperty(value = "切割效率",required = true)
    private String cuttingEff;

    /**
     * 钢板利用率
     */
    @ExcelProperty("钢板利用率")
    @ApiModelProperty(value = "钢板利用率",required = true)
    private String plateUtilRate;

    /**
     * 订单满足率
     */
    @ExcelProperty("订单满足率")
    @ApiModelProperty(value = "订单满足率",required = true)
    private String orderFulfillRate;

    /**
     * 余料率
     */
    @ExcelProperty("余料率")
    @ApiModelProperty(value = "余料率",required = true)
    private String scrapRate;


    /**
     * 报废率
     */
    @ExcelProperty("报废率")
    @ApiModelProperty(value = "报废率",required = true)
    private String wasteRate;

    /**
     * 钢板总重量
     */
    @ExcelProperty("钢板总重量")
    @ApiModelProperty(value = "钢板总重量",required = true)
    private String totalPlateWeight;

    /**
     * 零件总重量
     */
    @ExcelProperty("零件总重量")
    @ApiModelProperty(value = "零件总重量",required = true)
    private String totalPartWeight;

    /**
     * 余料总重量
     */
    @ExcelProperty("余料总重量")
    @ApiModelProperty(value = "余料总重量",required = true)
    private String totalScrapWeight;

    /**
     * 整版切割长度
     */
    @ExcelProperty("整版切割长度")
    @ApiModelProperty(value = "整版切割长度",required = true)
    private String  fullCutLength;

    /**
     * 整版理论切割时间
     */
    @ExcelProperty("整版理论切割时间")
    @ApiModelProperty(value = "整版理论切割时间",required = true)
    private String fullCutTheoTime;

    /**
     * 整版空走长度
     */
    @ExcelProperty("整版空走长度")
    @ApiModelProperty(value = "整版空走长度",required = true)
    private String idleTravelLength;

    /**
     * 整版空走时间
     */
    @ExcelProperty("整版空走时间")
    @ApiModelProperty(value = "整版空走时间",required = true)
    private String idleTravelTime;

    /**
     * 整版穿孔数
     */
    @ExcelProperty("整版穿孔数")
    @ApiModelProperty(value = "整版穿孔数",required = true)
    private Integer perfCount;
}
