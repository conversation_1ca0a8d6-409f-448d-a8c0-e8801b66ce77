package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/11/29
 */
@Data
public class WorkOrderRouteInfoTemplate {

    /**
     * 工序id
     */
    @ExcelProperty("工序id")
    @ApiModelProperty(value = "工序id", required = true)
    private String id;

    /**
     * 顺序
     */
    @ExcelProperty("顺序")
    @ApiModelProperty(value = "顺序", required = true)
    private int index;

    /**
     * 工序编号(工位编号)
     */
    @ExcelProperty("工序编号(工位编号)")
    @ApiModelProperty(value = "工序编号(工位编号)", required = true)
    private String stepCode;

    /**
     * 工序名称
     */
    @ExcelProperty("工序名称")
    @ApiModelProperty(value = "工序名称", required = true)
    private String stepName;
}
