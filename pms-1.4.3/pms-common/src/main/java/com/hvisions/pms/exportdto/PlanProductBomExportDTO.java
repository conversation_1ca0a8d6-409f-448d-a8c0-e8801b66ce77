package com.hvisions.pms.exportdto;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
public class PlanProductBomExportDTO {
    /**
     * 主键
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键Id")
    private Long id;

    /**
     * 计划ID
     */
//    @ApiModelProperty(value = "计划Id")
//    private Integer planId;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String planCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Long quantity;

    /**
     * 船型
     */
    @ApiModelProperty(value = "船型")
    private String model;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;

    /**
     * 是否坡口
     */
//    @ApiModelProperty(value = "是否坡口")
//    private String isGroove;

    /**
     * 坡口朝向
     */
//    @ApiModelProperty(value = "坡口朝向")
//    private String grooveDirection;


    /**
     * 零件长度（mm）
     */
    @ApiModelProperty(value = "零件长度（mm）")
    private String length;

    /**
     * 零件宽度（mm）
     */
    @ApiModelProperty(value = "零件宽度（mm）")
    private String width;

    /**
     * 零件厚度（mm）
     */
    @ApiModelProperty(value = "零件厚度（mm）")
    private String thick;

    /**
     * 零件类型
     */
    @ApiModelProperty(value = "零件类型")
    private String matType;

    /**
     * 零件净重
     */
    @ApiModelProperty(value = "零件毛重")
    private String roughWeight;

    /**
     * 零件净重
     */
    @ApiModelProperty(value = "零件净重")
    private String netWeight;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String unitMeasures;

    /**
     * 工艺路线编号
     */
//    @ApiModelProperty(value = "工艺路线编号")
//    private String routeCode;

    /**
     * 工艺路线版本
     */
//    @ApiModelProperty(value = "工艺路线版本")
//    private String routeVersion;

    /**
     * 料框编号
     */
    @ApiModelProperty(value = "料框编号")
    private String frameCode;

    /**
     * 实际数量
     */
    @ApiModelProperty(value = "实际数量")
    private Integer actQuantity;

    /**
     * 零件齐套时间
     */
    @Column(updatable = false)
    @ApiModelProperty(value = "零件齐套时间")
    protected Date completeTime;

}
