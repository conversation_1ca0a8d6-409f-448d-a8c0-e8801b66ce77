package com.hvisions.pms.changeshiftsdto;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: BuildEquipmentDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-21</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BuildWorkCenterDTO extends SysBaseDTO {


    @ApiModelProperty(value = "工位Id")
    private Integer workCenterId;

    @ApiModelProperty(value = "工位编码")
    private String workCenterCode;

    @ApiModelProperty(value = "工位名称")
    private String workCenterName;
}
