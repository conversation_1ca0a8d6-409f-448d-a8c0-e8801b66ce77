package com.hvisions.pms.exportdto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P> 调度记录物料信息导出   <P>
 *
 * <AUTHOR>
 * @date 2025/1/2
 */
@Data
public class AgvTaskRecordDetail0ExportDTO {

    /**
     * 主键ID
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "id",required = true)
    private Long id;

    /**
     * 主表ID
     */
    @ExcelAnnotation(ignore = true)
    @ExcelProperty(value = "主表ID")
    @ApiModelProperty(value = "主表ID")
    private Long raskRecordId;

    /**
     * 请求码
     */
    @ApiModelProperty(value = "请求码")
    @ExcelProperty(value = "请求码")
    private String requestCode;

    /**
     * pn码
     */
    @ApiModelProperty(value = "pn码")
    @ExcelProperty(value = "pn码")
    private String pn;

    /**
     * 物料编号
     */
    @ApiModelProperty(value = "物料编号")
    @ExcelProperty(value = "物料编号")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @ExcelProperty(value = "物料名称")
    private String materialName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @ExcelProperty(value = "数量")
    private Long quality;

    /**
     * 组立物料
     */
    @ApiModelProperty(value = "组立物料")
    @ExcelProperty(value = "组立物料")
    private String materialParentCode;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    @ExcelProperty(value = "工单编号")
    private String workOrderCode;

    /**
     * 组立工单号
     */
    @ApiModelProperty(value = "组立工单号")
    @ExcelProperty(value = "组立工单号")
    private String parentWorkOrderCode;


    /**
     * 流向
     */
    @ApiModelProperty(value = "流向")
    @ExcelProperty(value = "流向")
    private String blockCode;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    @ExcelProperty(value = "船号")
    private String shipNo;

    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    @ExcelProperty(value = "重量")
    private String  weight;

}
