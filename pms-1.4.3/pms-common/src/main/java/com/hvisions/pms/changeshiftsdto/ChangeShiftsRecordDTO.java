package com.hvisions.pms.changeshiftsdto;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: ChangeShiftsRecordDTO</p >
 * <p>Description: 交接班记录DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class ChangeShiftsRecordDTO extends SysBaseDTO {

    /**
     * 交班时间
     */
    @ApiModelProperty(value = "交班时间")
    private Date handTime;

    /**
     * 交班人
     */
    @ApiModelProperty(value = "交班人")
    private Integer handMan;


    /**
     * 交班人姓名
     */
    @ApiModelProperty(value = "交班人姓名")
    private String handManName;

    /**
     * 交班工作班次
     */
    @ApiModelProperty(value = "交班工作班次")
    private Date handShift;


    /**
     * 接班工作班次
     */
    @ApiModelProperty(value = "接班工作班次")
    private Date overShift;

    /**
     * 接班时间
     */
    @ApiModelProperty(value = "接班时间")
    private Date overTime;

    /**
     * 接班人
     */
    @NotNull(message = "请选择接班人")
    @ApiModelProperty(value = "接班人")
    private Integer overMan;


    /**
     * 接班人姓名
     */
    @ApiModelProperty(value = "接班人姓名")
    private String overManName;

    /**
     * 交接班状态  （1 交班中 / 2 驳回 /3   已接班）
     */
    @ApiModelProperty(value = "交接班状态")
    private Integer state;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 工位ID
     */
    @NotNull(message = "工位信息必传")
    @ApiModelProperty(value = "工位ID")
    private Integer workCenterId;

    /**
     * 工作时进行操作的工单列表
     */
    @ApiModelProperty(value = "工作时进行操作的工单列表")
    private List<ChangeShiftsWorkDTO> workDTOS;

    /**
     * 交班工作时段投入物料
     */
    @ApiModelProperty(value = "交班工作时段投入物料")
    private List<InputMaterialsDTO> inputMaterialsDTOList;
    /**
     * 交班时产出物料列表
     */
    @ApiModelProperty(value = "交班时产出物料列表")
    private List<ProductionMaterialsDTO> materialsDTOS;

    /**
     * 检查项目名称
     */
    @ApiModelProperty(value = "检查项目名称")
    private String InspectionItemName;

    /**
     * 岗位检查点记录
     */
    @ApiModelProperty(value = "岗位检查点记录")
    private List<InspectDTO> inspectDTOList;


}