package com.hvisions.pms.exportdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2025/1/13
 */
@Data
public class CallMaterialRecordExportDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;


    /**
     * 请求码
     */
    @ApiModelProperty(value = "请求码")
    private String requestCode;


    /**
     * 请求时间
     */
    @ApiModelProperty(value = "请求时间")
    private String requestTime;

    /**
     * 请求用户
     */
    @ApiModelProperty(value = "请求用户")
    private String requestUserCode;


    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrderCode;

    /**
     * 料框编号
     */
    @ApiModelProperty(value = "料框编号")
    private String frameCode;

    /**
     * 料点编号
     */
    @ApiModelProperty(value = "料点编号")
    private String pointCode;

    /**
     *  创建时间
     */
//    @ApiModelProperty(value = "创建时间")
//    private Date createTime;

}
