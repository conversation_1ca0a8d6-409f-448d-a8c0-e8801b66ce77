package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
@Data
public class xcMaterialCutPlanTemplate {

    /**
     * 作业编码
     */
    @ExcelProperty("作业编码")
    @ApiModelProperty(value = "作业编码",required = true)
    private String orderNo;

    /**
     * 船型
     */
    @ExcelProperty("船型")
    @ApiModelProperty(value = "船型",required = true)
    private String shipMode;

    /**
     * 船号
     */
    @ExcelProperty("船号")
    @ApiModelProperty(value = "船号",required = true)
    private String shipCode;

    /**
     * 产线编号
     */
    @ExcelProperty("产线编号")
    @ApiModelProperty(value = "产线编号",required = true)
    private String lineCode;

    /**
     * 计划结束时间
     */
    @ExcelProperty("计划结束时间")
    @ApiModelProperty(value = "计划结束时间",required = true)
    private Date planEndTime;

    /**
     * 分段号
     */
    @ExcelProperty("分段号")
    @ApiModelProperty(value = "分段号",required = true)
    private String segmentationCode;

    /**
     * 计划完成时间
     */
    @ExcelProperty("计划完成时间")
    @ApiModelProperty(value = "计划完成时间",required = true)
    private String prodDate;
}
