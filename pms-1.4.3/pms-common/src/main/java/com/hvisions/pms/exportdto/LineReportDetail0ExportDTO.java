package com.hvisions.pms.exportdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * <P> 报工详情导出   <P>
 *
 * <AUTHOR>
 * @date 2025/1/6
 */
@Data
public class LineReportDetail0ExportDTO {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID", notes = "主表ID")
    private long reportId;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号", notes = "工单号")
    private String orderCode;

    /**
     * 料框编号（托盘编号）
     */
    @ApiModelProperty(value = "料框编号", notes = "料框编号")
    private String frameCode;

    /**
     * 物料号
     */
    @ApiModelProperty(value = "物料号", notes = "物料号")
    private String materialCode;

    /**
     * 物料sn码
     */
    @ApiModelProperty(value = "物料sn码", notes = "物料sn码")
    private String pn;


    /**
     * 数量
     */
    @ApiModelProperty(value = "数量", notes = "数量")
    private Integer quality;

    /**
     * 合格数
     */
    @ApiModelProperty(value = "合格数", notes = "合格数")
    private Integer qualifiedQty;

    /**
     * 丢失数量
     */
    @ApiModelProperty(value = "丢失数量", notes = "丢失数量")
    private Integer lossQty;

    /**
     * 报废数量
     */
    @ApiModelProperty(value = "报废数量", notes = "报废数量")
    private Integer scrapQty;

    /**
     * 返修数量
     */
    @ApiModelProperty(value = "返修数量", notes = "返修数量")
    private Integer repairQty;
}
