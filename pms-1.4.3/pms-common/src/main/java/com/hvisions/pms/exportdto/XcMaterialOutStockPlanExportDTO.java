package com.hvisions.pms.exportdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <P> 型材出库计划导出  <P>
 *
 * <AUTHOR>
 * @date 2025/2/7
 */
@Data
public class XcMaterialOutStockPlanExportDTO {

    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号")
    private String taskNo;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String workOrderCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 型材原材规格
     */
    @ApiModelProperty(value = "型材原材规格")
    private String sepces;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer quantity;

    /**
     * 上料位置
     */
    @ApiModelProperty(value = "上料位置")
    private String location;

    /**
     * 备用1
     */
    @ApiModelProperty(value = "备用1")
    private String udf1;

    /**
     * 备用2
     */
    @ApiModelProperty(value = "备用2")
    private String udf2;

    /**
     * 备用3
     */
    @ApiModelProperty(value = "备用3")
    private String udf3;

    /**
     * 状态(0:新建,1:已下发,2:下发失败)
     */
    @ApiModelProperty(value = "状态(0:新建,1:已下发,2:下发失败)")
    private Integer status;

    /**
     * 下发操作人姓名
     */
    @ApiModelProperty(value = "下发操作人姓名")
    private String userName;

    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间")
    private Date sendTime;
}
