package com.hvisions.pms.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>Title: ActualAndPlan</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/5/28</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class ActualAndPlan {
    /**
    *   物料名称
    */
    @ApiModelProperty(value = "物料名称")
    String materialName;
    /**
    *   实际用量
    */
    @ApiModelProperty(value = "实际用量")
    BigDecimal actual;
    /**
    *   计划用量
    */
    @ApiModelProperty(value = "计划用量")
    BigDecimal plan;
}









