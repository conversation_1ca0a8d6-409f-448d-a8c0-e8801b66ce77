package com.hvisions.pms.inspectdto;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: CheckPointDTO</p >
 * <p>Description: 检查点</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class CheckPointDTO extends SysBaseDTO {


    /**
     * 检查点名称
     */
    @ApiModelProperty(value = "检查点名称")
    private String checkPointName;

    /**
     * 检查项目ID
     */
    @ApiModelProperty(value = "检查项目ID")
    private Integer InspectionItemId;
}