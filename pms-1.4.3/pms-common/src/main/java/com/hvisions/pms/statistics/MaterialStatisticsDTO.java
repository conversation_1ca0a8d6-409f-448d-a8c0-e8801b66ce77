package com.hvisions.pms.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>Title: MaterialStatisticsDTO</p>
 * <p>Description: 物料统计</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/4/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class MaterialStatisticsDTO {
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;
    /**
     * 物料数量
     */
    @ApiModelProperty(value = "物料数量")
    private BigDecimal quantity;
    /**
     * 产线名称
     */
    @ApiModelProperty(value = "产线名称")
    private String cellName;
    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称")
    private String crewName;
}









