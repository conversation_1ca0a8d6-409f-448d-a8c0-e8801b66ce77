package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/5/7
 */
@Data
public class XcMaterialOutStockPlanTemplate {
    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String workOrderCode;

    /**
     * 物料编码
     */
    @ExcelProperty("物料编码")
    @ApiModelProperty(value = "物料编码", required = true)
    private String materialCode;

    /**
     * 型材原材规格
     */
    @ExcelProperty("型材原材规格")
    @ApiModelProperty(value = "型材原材规格", required = true)
    private String sepces;

    /**
     * 数量
     */
    @ExcelProperty("数量")
    @ApiModelProperty(value = "数量", required = true)
    private Integer quantity;

    /**
     * 上料位置
     */
    @ExcelProperty("上料位置")
    @ApiModelProperty(value = "上料位置", required = true)
    private String location;

    /**
     * 备用1
     */
    @ExcelProperty("备用1")
    @ApiModelProperty(value = "备用1", required = true)
    private String udf1;

    /**
     * 备用2
     */
    @ExcelProperty("备用2")
    @ApiModelProperty(value = "备用2", required = true)
    private String udf2;

    /**
     * 备用3
     */
    @ExcelProperty("备用3")
    @ApiModelProperty(value = "备用3", required = true)
    private String udf3;

}
