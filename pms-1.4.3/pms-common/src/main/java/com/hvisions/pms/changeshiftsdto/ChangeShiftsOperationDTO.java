package com.hvisions.pms.changeshiftsdto;

import com.hvisions.pms.dto.OrderOperationDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: ChangeShiftsOperationDTO</p >
 * <p>Description: 交接班工序</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-17</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class ChangeShiftsOperationDTO extends OrderOperationDTO {

    /**
     * 交班工作时段投入物料
     */
    @ApiModelProperty(value = "交班工作时段投入物料")
    private List<InputMaterialsDTO> inputMaterialsDTOList;
    /**
     * 交班时产出物料列表
     */
    @ApiModelProperty(value = "交班时产出物料列表")
    private List<ProductionMaterialsDTO> materialsDTOS;
}