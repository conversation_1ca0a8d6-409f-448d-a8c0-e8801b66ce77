package com.hvisions.pms.materialdto;

import com.hvisions.pms.dto.OperationMaterialDTO;
import com.hvisions.pms.dto.OperationOutPutMaterialDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: OperationMaterialsDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OperationMaterialsDTO {

    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private Integer operationId;


    /**
     * 工序编码
     */
    @ApiModelProperty(value = "工序编码")
    private String operationCode;

    /**
     * 工序产出料
     */
    @ApiModelProperty(value = "工序产出料")
    private List<OperationOutPutMaterialDTO> operationOutPutMaterialDTO;

    /**
     * 工序投入料
     */
    @ApiModelProperty(value = "工序投入料")
    private List<OperationMaterialDTO> operationMaterialDTOS;

}
