package com.hvisions.pms.exportdto;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>  打磨计划导出：零件信息DTO  <P>
 *
 * <AUTHOR>
 * @date 2025/1/2
 */
@Data
public class PolishPlanExportDetail0DTO {

    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Long id;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private Long codeId;

    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号")
    private String code;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String material_code;

    /**
     * 零件工单号
     */
    @ApiModelProperty(value = "零件工单号")
    private String work_order_code;

    /**
     * PN码
     */
    @ApiModelProperty(value = "PN码")
    private String sn;

    /**
     * 零件物料名称
     */
    @ApiModelProperty(value = "零件物料名称")
    private String material_name;

    /**
     * 加工代码
     */
    @ApiModelProperty(value = "加工代码")
    private String gps2;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer mat_qty;

    /**
     * 合格数
     */
    @ApiModelProperty(value = "合格数")
    private Integer qualified_qty;

    /**
     * 报废数量
     */
    @ApiModelProperty(value = "报废数量")
    private Integer scrap_qty;

    /**
     * 托盘
     */
    @ApiModelProperty(value = "托盘")
    private String pallet_code;
}
