package com.hvisions.pms.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: CrewMaterial</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/4/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class CrewMaterial {
    public CrewMaterial(String crewName) {
        this.crewName = crewName;
        this.products = new ArrayList<>();
    }

    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称")
    private String crewName;
    /**
     * 产物信息
     */
    @ApiModelProperty(value = "产物信息")
    private List<MaterialStatisticsDTO> products;
}









