package com.hvisions.pms.exportdto;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>  零件信息导出DTO  <P>
 *
 * <AUTHOR>
 * @date 2024/12/24
 */
@Data
public class XcMaterialCutPlanDetail1ExportDTO {

    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Long id;


    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private Long orderId;

    /**
     * 作业编号
     */
    @ApiModelProperty(value = "作业编号")
    private String orderNo;

    /**
     * 父表id
     */
    @ApiModelProperty(value = "父表id")
    private Long subPlanId;

    /**
     * 切割计划子编号
     */
    @ApiModelProperty(value = "切割计划子编号")
    private String subPlanNo;

    /**
     * 零件工单编号
     */
    @ApiModelProperty(value = "零件工单编号")
    private String workOrderCode;

    /**
     * 物料号
     */
    @ApiModelProperty(value = "物料号")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 是否外发 0否，1是
     */
    @ApiModelProperty(value = "是否外发")
    private String outFlag;

    /**
     * 自制外协 0自制，1外协
     */
    @ApiModelProperty(value = "自制外协")
    private String specialPurchaseTypeCode;

    /**
     * 流向代码
     */
    @ApiModelProperty(value = "流向代码")
    private String blockCode;

    /**
     * 套料数量
     */
    @ApiModelProperty(value = "套料数量")
    private Integer  nestingCount;

    /**
     * 订单数量
     */
    @ApiModelProperty(value = "订单数量")
    private String orderCount;

    /**
     * 零件长度
     */
    @ApiModelProperty(value = "零件长度")
    private String partnetLength;


    /**
     * 当前零件所属分段号
     */
    @ApiModelProperty(value = "当前零件所属分段号")
    private String segmentationCode;
}
