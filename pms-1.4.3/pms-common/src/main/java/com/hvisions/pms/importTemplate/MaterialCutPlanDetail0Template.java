package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/4/24 14:52
 * 钢板切割计划的零件信息导入模版
 */
@Data
public class MaterialCutPlanDetail0Template {
    /**
     * 切割计划编码
     */
    @ExcelProperty("切割计划编码")
    @ApiModelProperty(value = "切割计划编码", required = true)
    private String cutPlanCode;

    /**
     * 余料(0:是,1:否)
     */
    @ExcelProperty("余料(0:是,1:否)")
    @ApiModelProperty(value = "余料(0:是,1:否)", required = true)
    private Integer surplusMaterialFlag;

    /**
     * 物料号
     */
    @ExcelProperty("物料号")
    @ApiModelProperty(value = "物料号", required = true)
    private String materialCode;

    /**
     * 物料名称
     */
    @ExcelProperty("物料名称")
    @ApiModelProperty(value = "物料名称", required = true)
    private String materialName;

    /**
     * 数量
     */
    @ExcelProperty("数量")
    @ApiModelProperty(value = "数量", required = true)
    private Integer quality;

    /**
     * 工单号
     */
    @ExcelProperty("工单号")
    @ApiModelProperty(value = "工单号", required = true)
    private String workOrder;

    /**
     * 零件图
     */
    @ExcelProperty("零件图")
    @ApiModelProperty(value = "零件图", required = true)
    private String materialPicture;

    /**
     * pn
     */
    @ExcelProperty("pn")
    @ApiModelProperty(value = "pn", required = true)
    private String pn;

    /**
     * 长度
     */
    @ExcelProperty("长度")
    @ApiModelProperty(value = "长度", required = true)
    private String  length;

    /**
     * 宽度
     */
    @ExcelProperty("宽度")
    @ApiModelProperty(value = "宽度", required = true)
    private String  width;

    /**
     * 厚度
     */
    @ExcelProperty("厚度")
    @ApiModelProperty(value = "厚度", required = true)
    private String  thick;


    /**
     * 零件序号
     */
    @ExcelProperty("零件序号")
    @ApiModelProperty(value = "no", required = true)
    private String no;

    /**
     * 套料数量
     */
    @ExcelProperty("套料数量")
    @ApiModelProperty(value = "套料数量", required = true)
    private Integer  nestingCount;

    /**
     * 订单数量
     */
    @ExcelProperty("订单数量")
    @ApiModelProperty(value = "订单数量", required = true)
    private Integer orderCount;

    /**
     * 零件净重量
     */
    @ExcelProperty("零件净重量")
    @ApiModelProperty(value = "零件净重量", required = true)
    private String  partNetWeight;

    /**
     * 零件矩形重量
     */
    @ExcelProperty("零件矩形重量")
    @ApiModelProperty(value = "零件矩形重量", required = true)
    private String  partRectWeight;

    /**
     * 零件净面积
     */
    @ExcelProperty("零件净面积")
    @ApiModelProperty(value = "零件净面积", required = true)
    private String   partNetArea;

    /**
     * 零件矩形面积
     */
    @ExcelProperty("零件矩形面积")
    @ApiModelProperty(value = "零件矩形面积", required = true)
    private String  partRectArea;

    /**
     * 单个零件切割长度
     */
    @ExcelProperty("单个零件切割长度")
    @ApiModelProperty(value = "单个零件切割长度", required = true)
    private String  singlePartCutLength;

    /**
     * 单个零件打标长度
     */
    @ExcelProperty("单个零件打标长度")
    @ApiModelProperty(value = "单个零件打标长度", required = true)
    private String  singlePartMarkingLength;

    /**
     * 单个零件刺穿孔数
     */
    @ExcelProperty("单个零件刺穿孔数")
    @ApiModelProperty(value = "单个零件刺穿孔数", required = true)
    private Integer  singlePartPierceCount;

}
