package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
@Data
public class xcMaterialCutPlanDetail0Template {

    /**
     * 作业编号
     */
    @ExcelProperty("作业编码")
    @ApiModelProperty(value = "作业编码",required = true)
    private String orderNo;

    /**
     * 切割计划子编号
     */
    @ExcelProperty("切割计划子编号")
    @ApiModelProperty(value = "切割计划子编号",required = true)
    private String subPlanNo;

    /**
     * 零件数量
     */
    @ExcelProperty("零件数量")
    @ApiModelProperty(value = "零件数量",required = true)
    private String qty;

    /**
     * GEN文件地址
     */
    @ExcelProperty("GEN文件地址")
    @ApiModelProperty(value = "GEN文件地址",required = true)
    private String genFilePath;

    /**
     * 废料总长度（型材-零件-余料=废料）
     */
    @ExcelProperty("废料总长度")
    @ApiModelProperty(value = "废料总长度",required = true)
    private String scrapLength;

    /**
     * 型材利用率
     */
    @ExcelProperty("型材利用率")
    @ApiModelProperty(value = "型材利用率",required = true)
    private String plateUtilRate;

    /**
     * 订单满足率
     */
    @ExcelProperty("订单满足率")
    @ApiModelProperty(value = "订单满足率",required = true)
    private String orderFulfillRate;

    /**
     * 余料率
     */
    @ExcelProperty("余料率")
    @ApiModelProperty(value = "余料率",required = true)
    private String scrapRate;

    /**
     * 报废率
     */
    @ExcelProperty("报废率")
    @ApiModelProperty(value = "报废率",required = true)
    private String wasteRate;

    /**
     *型材总长度
     */
    @ExcelProperty("型材总长度")
    @ApiModelProperty(value = "型材总长度",required = true)
    private String totalPlateLength;

    /**
     * 零件总长度
     */
    @ExcelProperty("零件总长度")
    @ApiModelProperty(value = "零件总长度",required = true)
    private String totalPartLength;

    /**
     * 余料总长度
     */
    @ExcelProperty("余料总长度")
    @ApiModelProperty(value = "余料总长度",required = true)
    private String totalScrapLength;

}
