package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/11/29
 */
@Data
public class WorkOrderBomInfoTemplate {

    /**
     * 零件物料编号
     */
    @ExcelProperty("零件物料编号")
    @ApiModelProperty(value = "零件物料编号", required = true)
    private String materialCode;

    /**
     * 零件名称
     */
    @ExcelProperty("零件名称")
    @ApiModelProperty(value = "零件名称", required = true)
    private String materialName;

    /**
     * 数量
     */
    @ExcelProperty("数量")
    @ApiModelProperty(value = "数量", required = true)
    private Long quantity;

    /**
     * 船型
     */
    @ExcelProperty("船型")
    @ApiModelProperty(value = "船型", required = true)
    private String model;

    /**
     * 分段号
     */
    @ExcelProperty("分段号")
    @ApiModelProperty(value = "分段号", required = true)
    private String segmentationCode;

    /**
     * 零件长度（mm）
     */
    @ExcelProperty("零件长度（mm）")
    @ApiModelProperty(value = "零件长度（mm）", required = true)
    private String length;

    /**
     * 零件宽度（mm）
     */
    @ExcelProperty("零件宽度（mm）")
    @ApiModelProperty(value = "零件宽度（mm）", required = true)
    private String width;

    /**
     * 零件厚度（mm）
     */
    @ExcelProperty("零件厚度（mm）")
    @ApiModelProperty(value = "零件厚度（mm）", required = true)
    private String thick;

    /**
     * 零件类型（mm）
     */
    @ExcelProperty("零件类型（mm）")
    @ApiModelProperty(value = "零件类型（mm）", required = true)
    private String matType;

    /**
     * 零件净重（mm）
     */
    @ExcelProperty("零件净重（mm）")
    @ApiModelProperty(value = "零件净重（mm）", required = true)
    private String netWeight;

    /**
     * 计量单位（mm）
     */
    @ExcelProperty("计量单位（mm）")
    @ApiModelProperty(value = "计量单位（mm）", required = true)
    private String unitMeasures;
}
