package com.hvisions.pms.materialdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: MaterialTraceDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/1/18</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class MaterialTraceDTO {

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Integer orderId;

    @ApiModelProperty(value = "工单编码")
    private String orderCode;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "物料批次号")
    private String batchNumber;

    /**
     * 产出物料编码
     */
    @ApiModelProperty(value = "产出物料编码")
    private String materialCode;

    /**
     * 产出物料名称
     */
    @ApiModelProperty(value = "产出物料名称")
    private String materialName;

    private String bomVersion;

    private BigDecimal planCount;

    private BigDecimal actualCount;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;


}