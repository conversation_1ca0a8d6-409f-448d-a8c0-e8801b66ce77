package com.hvisions.pms;

/**
 * <p>Title: OrderMqConst</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/9</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface OrderMqConst {

    /**
     * 工单exchange
     */
    String WORK_ORDER_EXCHANGE = "hvisions.hipermatic.workorder";

    /**
     * 任务exchange
     */
    String TASK_EXCHANGE = "hvisions.hipermatic.task";

    /**
     * 工单创建路由键
     */
    String CRATE = "create";
    /**
     * 工单下发路由键
     */
    String ISSUED = "issued";

    /**
     * 工单撤销
     */
    String ON_ISSUED = "onIssued";
    /**
     * 工单暂停路由键
     */
    String WORK_PAUSE = "pause";
    /**
     * 工单继续路由键
     */
    String CONTINUE = "continue";
    /**
     * 工单报废
     */
    String SCRAP = "scrap";
    /**
     * 工单完工
     */
    String COMPLETE = "complete";
    /**
     * 工单开始
     */
    String ORDER_START = "start";
    /**
     * 任务开始
     */
    String TASK_START = "start";
    /**
     * 任务暂停路由键
     */
    String TASK_PAUSE = "pause";
    /**
     * 任务继续
     */
    String TASK_CONTINUE = "continue";
    /**
     * 任务结束
     */
    String TASK_COMPLETE = "complete";

    String ORDER_FINISH = "finish";
}