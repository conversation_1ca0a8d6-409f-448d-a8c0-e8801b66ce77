package com.hvisions.pms.changeshiftsdto;

import com.hvisions.pms.dto.OpeationInputMaterialDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: InputMaterialsDTO</p >
 * <p>Description: 交班工作时段投入物料</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InputMaterialsDTO extends OpeationInputMaterialDTO {

    /**
     * 交接班记录ID
     */
    @ApiModelProperty(value = "交接班记录ID")
    private Integer changeShiftsId;

    /**
     * 工单ID
     */
    @ApiModelProperty(value = "工单ID")
    private Integer orderId;
}