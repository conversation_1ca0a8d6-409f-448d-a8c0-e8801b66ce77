package com.hvisions.pms;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title: SysBaseDTO</p>
 * <p>Description: Dto尽量继承，可以做出自己的更改</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/09</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class SysBaseDTO {


    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Integer id;

    /**
     * 创建时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建时间", notes = "此字段不必传递", readOnly = true)
    protected Date createTime = new Date();

    /**
     * 修改时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新时间", notes = "此字段不必传递", readOnly = true)
    protected Date updateTime = new Date();

    /**
     * 创建人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建用户Id", notes = "创建记录时传递", readOnly = true)
    protected Integer creatorId = 0;

    /**
     * 修改人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新用户Id", notes = "更新记录时传递", readOnly = true)
    protected Integer updaterId = 0;


    /**
     * 系统代码
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "系统代码", readOnly = true)
    protected String siteNum;

}
