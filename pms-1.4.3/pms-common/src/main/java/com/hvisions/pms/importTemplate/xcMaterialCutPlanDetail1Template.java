package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
@Data
public class xcMaterialCutPlanDetail1Template {

    /**
     * 作业编号
     */
    @ExcelProperty("作业编码")
    @ApiModelProperty(value = "作业编码",required = true)
    private String orderNo;

    /**
     * 切割计划子编号
     */
    @ExcelProperty("切割计划子编号")
    @ApiModelProperty(value = "切割计划子编号",required = true)
    private String subPlanNo;

    /**
     * 零件工单编号
     */
    @ExcelProperty("零件工单编号")
    @ApiModelProperty(value = "零件工单编号",required = true)
    private String workOrderCode;

    /**
     * 物料号
     */
    @ExcelProperty("物料号")
    @ApiModelProperty(value = "物料号",required = true)
    private String materialCode;

    /**
     * 物料名称
     */
    @ExcelProperty("物料名称")
    @ApiModelProperty(value = "物料名称",required = true)
    private String materialName;

    /**
     * 是否外发 0否，1是
     */
    @ExcelProperty("是否外发(0:否,1:是)")
    @ApiModelProperty(value = "是否外发(0:否,1:是)",required = true)
    private String outFlag;

    /**
     * 自制外协 0自制，1外协
     */
    @ExcelProperty("自制外协(0:自制,1:外协)")
    @ApiModelProperty(value = "自制外协(0:自制,1:外协)",required = true)
    private String specialPurchaseTypeCode;

    /**
     * 流向代码
     */
    @ExcelProperty("流向代码")
    @ApiModelProperty(value = "流向代码",required = true)
    private String blockCode;

    /**
     * 套料数量
     */
    @ExcelProperty("套料数量")
    @ApiModelProperty(value = "套料数量",required = true)
    private Integer  nestingCount;

    /**
     * 订单数量
     */
    @ExcelProperty("订单数量")
    @ApiModelProperty(value = "订单数量",required = true)
    private String orderCount;

    /**
     * 零件长度
     */
    @ExcelProperty("零件长度")
    @ApiModelProperty(value = "零件长度",required = true)
    private String partnetLength;
}
