package com.hvisions.pms.changeshiftsdto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: ChangeShiftsRecordQueryDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-14</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class ChangeShiftsRecordQueryDTO extends PageInfo {

    /**
     * 交班人
     */
    @ApiModelProperty(value = "交班人")
    private Integer handMan;

    /**
     * 接班人
     */
    @ApiModelProperty(value = "接班人")
    private Integer overMan;
}