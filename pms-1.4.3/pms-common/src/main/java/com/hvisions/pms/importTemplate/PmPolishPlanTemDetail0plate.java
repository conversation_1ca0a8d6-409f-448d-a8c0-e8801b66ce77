package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
@Data
public class PmPolishPlanTemDetail0plate {


    /**
     * 任务编号
     */
    @ExcelProperty("任务编号")
    @ApiModelProperty(value = "任务编号")
    private String code;

    /**
     * 物料编码
     */
    @ExcelProperty("物料编码")
    @ApiModelProperty(value = "物料编码")
    private String material_code;

    /**
     * 物料编码
     */
    @ExcelProperty("物料编码")
    @ApiModelProperty(value = "物料编码")
    private String material_name;

    /**
     * 零件工单号
     */
    @ExcelProperty("零件工单编码")
    @ApiModelProperty(value = "零件工单编码")
    private String work_order_code;

    /**
     * PN码
     */
    @ExcelProperty("PN码")
    @ApiModelProperty(value = "PN码")
    private String sn;



    /**
     * 加工代码
     */
    @ExcelProperty("加工代码")
    @ApiModelProperty(value = "加工代码")
    private String gps2;

    /**
     * 数量
     */
    @ExcelProperty("数量")
    @ApiModelProperty(value = "数量")
    private Integer mat_qty;

    /**
     * 合格数
     */
    @ExcelProperty("合格数")
    @ApiModelProperty(value = "合格数")
    private Integer qualified_qty;

    /**
     * 报废数量
     */
    @ExcelProperty("报废数量")
    @ApiModelProperty(value = "报废数量")
    private Integer scrap_qty;
}
