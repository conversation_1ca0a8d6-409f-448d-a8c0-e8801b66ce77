package com.hvisions.pms.materialdto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title: TraceQuery</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/1/18</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class TraceQuery extends PageInfo {
    private String batchNum;

    private String materialCode;

    private String materialName;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}