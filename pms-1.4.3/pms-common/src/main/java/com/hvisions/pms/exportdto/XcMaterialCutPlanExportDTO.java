package com.hvisions.pms.exportdto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <P>  型材切割计划导出DTO  <P>
 *
 * <AUTHOR>
 * @date 2024/12/24
 */
@Data
public class XcMaterialCutPlanExportDTO {


    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Long id;

    /**
     * 创建时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建时间", notes = "此字段不必传递", readOnly = true)
    protected Date createTime = new Date();

    /**
     * 修改时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新时间", notes = "此字段不必传递", readOnly = true)
    protected Date updateTime = new Date();

    /**
     * 创建人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建用户Id", notes = "创建记录时传递", readOnly = true)
    protected Integer creatorId = 0;

    /**
     * 修改人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新用户Id", notes = "更新记录时传递", readOnly = true)
    protected Integer updaterId = 0;

    /**
     * 作业编号
     */
    @ApiModelProperty(value = "作业编号")
    private String orderNo;

    /**
     * 船型
     */
    @ApiModelProperty(value = "船型")
    private String shipMode;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipCode;

    /**
     * 产线id
     */
    @ApiModelProperty(value = "产线id")
    private Integer lineId;

    /**
     * 产线编号
     */
    @ApiModelProperty(value = "产线编号")
    private String lineCode;

    /**
     * 产线名称
     */
    @ApiModelProperty(value = "产线名称")
    private String lineName;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planEndTime;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    private String prodDate;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 状态描述
     */
    @ApiModelProperty(value = "状态描述")
    private String statusDes;

    /**
     * 下发操作人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "下发操作人")
    private Integer sendUserId;

    /**
     * 下发操作人姓名
     */
    @ApiModelProperty(value = "下发操作人姓名")
    private String userName;

    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间")
    private Date sendTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private Date finishTime;

}
