package com.hvisions.pms.exportdto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Data
public class MaterialCutPlanDetail2ExportDTO {
    /**
     * 主键
     */
    @ExcelProperty("id")
    @ApiModelProperty(value = "id",required = true)
    protected Long id;

    /**
     * 主表id
     */
    @ExcelProperty("主表id")
    @ApiModelProperty(value = "主表id",required = true)
    private Long cutPlanId;

    /**
     * 切割计划
     */
    @ExcelProperty("主表id")
    @ApiModelProperty(value = "主表id",required = true)
    private String cutPlanCode;

    /**
     * 操作类型 （堆场出库、理料间入库、理料间出库、切割开始、完成）
     */
    @ExcelProperty("操作类型")
    @ApiModelProperty(value = "操作类型",required = true)
    private String operationType;

    /**
     *  开始时间
     */
    @ExcelProperty("开始时间")
    @ApiModelProperty(value = "开始时间",required = true)
    private String startTime;

    /**
     *  结束时间
     */
    @ExcelProperty("结束时间")
    @ApiModelProperty(value = "结束时间",required = true)
    private String endTime;
}
