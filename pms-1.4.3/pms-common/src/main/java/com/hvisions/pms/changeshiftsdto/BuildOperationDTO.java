package com.hvisions.pms.changeshiftsdto;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: BuildOperationDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-16</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class BuildOperationDTO extends SysBaseDTO {

    /**
     * 检查项目ID
     */
    @ApiModelProperty(value = "检查项目ID")
    private Integer inspectionItemId;

    /**
     * 工位ID
     */
    @ApiModelProperty(value = "工位ID")
    private Integer workCenterId;
}