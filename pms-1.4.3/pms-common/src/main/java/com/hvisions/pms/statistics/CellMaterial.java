package com.hvisions.pms.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: CellMaterial</p>
 * <p>Description: 产线信息</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/4/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class CellMaterial {

    public CellMaterial(String cellName){
        this.cellName = cellName;
        this.setProducts(new ArrayList<>());
    }
    /**
     * 产线信息
     */
    @ApiModelProperty(value = "产线信息")
    private String cellName;
    /**
     * 产出物信息
     */
    @ApiModelProperty(value = "产出物信息")
    private List<MaterialStatisticsDTO> products;
}









