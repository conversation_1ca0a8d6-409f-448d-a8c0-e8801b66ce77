package com.hvisions.pms.importTemplate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description HvPmCutPlanPassPointTemplate
 * <AUTHOR>
 * @Date 2024-05-11
 */
@Data
public class HvPmCutPlanPassPointTemplate {

    @NumberFormat(value = "#")
    @ExcelProperty("切割计划ID")
    @ApiModelProperty(value = "切割计划ID", required = true)
    private Long cutPlanId;
    /**
     * 切割计划编号
     */
    @ApiModelProperty(value = "切割计划编号", required = true)
    @ExcelProperty("切割计划编号")
    private String cutPlanNo;
    /**
     * 工位编号
     */
    @ExcelProperty("工位编号")
    @ApiModelProperty(value = "工位编号", required = true)
    private String stationCode;
    /**
     * 工位名称
     */
    @ExcelProperty("工位名称")
    @ApiModelProperty(value = "工位名称", required = true)
    private String stationName;
    /**
     * 通过时间
     */
    @ExcelProperty("通过时间")
    @ApiModelProperty(value = "通过时间", required = true)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date passTime;
}
