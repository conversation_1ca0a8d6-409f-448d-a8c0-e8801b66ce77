package com.hvisions.pms.inspectdto;

import com.hvisions.pms.SysBaseDTO;
import com.hvisions.pms.changeshiftsdto.BuildWorkCenterDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: InspectionItemDTO</p >
 * <p>Description: 检查项目</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class InspectionItemDTO extends SysBaseDTO {

    /**
     * 检查项目名称
     */
    @ApiModelProperty(value = "检查项目名称")
    private String InspectionItemName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String desc;

    /**
     * 检查点列表
     */
    @ApiModelProperty(value = "检查点列表")
    private List<CheckPointDTO> checkPointDTOS;

    /**
     * 设备信息列表
     */
    @ApiModelProperty(value = "设备信息列表")
    private List<BuildWorkCenterDTO> buildWorkCenterDTOList;
}