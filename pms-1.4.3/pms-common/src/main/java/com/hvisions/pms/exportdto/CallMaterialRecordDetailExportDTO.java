package com.hvisions.pms.exportdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2025/1/13
 */
@Data
public class CallMaterialRecordDetailExportDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;


    /**
     * 叫料记录ID
     */
    @ApiModelProperty(value = "叫料记录ID")
    private Long recordId;

    /**
     * 请求码
     */
    @ApiModelProperty(value = "请求码")
    private String  requestCode;

    /**
     * 物料编号
     */
    @ApiModelProperty(value = "物料编号")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;


}
