package com.hvisions.pms.exportdto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Data
public class MaterialCutPlanDetail1ExportDTO {

    /**
     * id
     */
    @ExcelProperty("id")
    @ApiModelProperty(value = "id",required = true)
    protected Long id;

    /**
     * 切割计划编码
     */
    @ExcelProperty("切割计划编码")
    @ApiModelProperty(value = "切割计划编码", required = true)
    private String cutPlanCode;

    /**
     * 余料号
     */
    @ExcelProperty("余料号")
    @ApiModelProperty(value = "余料号", required = true)
    private String materialCode;

    /**
     * 长度
     */
    @ExcelProperty("长度")
    @ApiModelProperty(value = "长度", required = true)
    private String length;

    /**
     * 宽度
     */
    @ExcelProperty("宽度")
    @ApiModelProperty(value = "宽度", required = true)
    private String width;

    /**
     * 厚度
     */
    @ExcelProperty("厚度")
    @ApiModelProperty(value = "厚度", required = true)
    private String thickness;

    /**
     * 重量
     */
    @ExcelProperty("重量")
    @ApiModelProperty(value = "重量", required = true)
    private String weight;
}
