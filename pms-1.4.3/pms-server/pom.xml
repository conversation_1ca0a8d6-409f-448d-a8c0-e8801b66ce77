<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <!-- 模型版本号，Maven 使用这个版本号来解析和理解 POM 文件的结构 -->
    <modelVersion>4.0.0</modelVersion>

    <!-- 父项目的配置 -->
    <parent>
        <groupId>com.hvisions</groupId> <!-- 父项目的组织标识符 -->
        <artifactId>pms</artifactId> <!-- 父项目的唯一标识符 -->
        <version>1.4.3</version> <!-- 父项目的版本号 -->
    </parent>

    <!-- 项目的唯一标识符 -->
    <artifactId>pms-server</artifactId>

    <!-- 项目的打包方式，使用 profiles 控制打包类型 -->
    <packaging>${packing-type}</packaging>

    <!-- 定义项目的 profiles -->
    <profiles>
        <!-- jar 包 profile -->
        <profile>
            <id>jar</id>
            <!-- 默认激活 -->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <packing-type>jar</packing-type> <!-- 打包类型为 jar -->
            </properties>
        </profile>

        <!-- war 包 profile -->
        <profile>
            <id>war</id>
            <properties>
                <packing-type>war</packing-type> <!-- 打包类型为 war -->
            </properties>
            <dependencies>
                <!-- 移除嵌入式 tomcat 插件 -->
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                    <exclusions>
                        <exclusion>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-starter-tomcat</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
                <!-- 添加 javax.servlet-api -->
                <dependency>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                    <version>3.1.0</version>
                </dependency>
                <!-- 添加 spring-boot-starter-tomcat，作用域为 provided -->
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <!-- 项目的直接依赖 -->
    <dependencies>
        <!-- Spring Boot Starter Data Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- Spring Cloud Starter Alibaba Nacos Discovery -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Spring Cloud Starter Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Spring Boot Starter Data JPA -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <!-- Spring Boot Starter Thymeleaf -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <!-- Spring Boot Starter Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- MyBatis Plus Boot Starter -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.1</version>
            <optional>true</optional>
        </dependency>

        <!-- MyBatis Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
            <version>3.5.1</version>
            <optional>true</optional>
        </dependency>

        <!-- MySQL Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.11</version>
        </dependency>

        <!-- Microsoft SQL Server JDBC Driver -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Spring Boot Starter Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- Spring Boot Starter Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- SpringFox Swagger 2 -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>

        <!-- Swagger 2 Markup -->
        <dependency>
            <groupId>io.github.swagger2markup</groupId>
            <artifactId>swagger2markup</artifactId>
            <version>1.3.3</version>
        </dependency>

        <!-- SpringFox Swagger UI -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>

        <!-- 自定义依赖：PMS Common -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>pms-common</artifactId>
            <version>1.4.3</version>
        </dependency>

        <!-- 自定义依赖：Framework Log Spring Boot Starter -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>framework-log-spring-boot-starter</artifactId>
            <version>1.4.0</version>
        </dependency>

        <!-- 自定义依赖：Hiper Base Client -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>hiper-base-client</artifactId>
            <version>1.5.14</version>
        </dependency>

        <!-- 自定义依赖：Hiper Base Common -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>hiper-base-common</artifactId>
            <version>1.5.14</version>
        </dependency>

        <!-- 自定义依赖：Framework Client -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>framework-client</artifactId>
            <version>1.7.0</version>
        </dependency>

        <!-- Spring Boot Starter AMQP -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <!-- 自定义依赖：Hvisions Common Spring Boot Starter -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>hvisions-common-springboot-starter</artifactId>
            <version>3.5.1</version>
        </dependency>

        <!-- 自定义依赖：Thirdparty Common -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>thirdparty-common</artifactId>
            <version>1.1.1</version>
        </dependency>

        <!-- 自定义依赖：WMS Common -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>wms-common</artifactId>
            <version>1.2.5</version>
        </dependency>

        <!-- EasyExcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.0.5</version>
        </dependency>

        <!-- 自定义依赖：Thirdparty Client -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>thirdparty-client</artifactId>
            <version>1.1.1</version>
        </dependency>

        <!-- 自定义依赖：WMS Client -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>wms-client</artifactId>
            <version>1.2.5</version>
        </dependency>

        <!-- JUnit -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <!-- Redisson -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.17.6</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <!-- 项目的构建配置 -->
    <build>
        <plugins>
            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <!-- Maven Surefire Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip> <!-- 跳过测试 -->
                </configuration>
            </plugin>
        </plugins>

        <!-- 资源文件配置 -->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <!-- 项目的发布仓库配置 -->
    <distributionManagement>
        <repository>
            <id>release</id>
            <url>http://**************:8022/repository/maven-server-release/</url>
        </repository>
    </distributionManagement>

</project>
