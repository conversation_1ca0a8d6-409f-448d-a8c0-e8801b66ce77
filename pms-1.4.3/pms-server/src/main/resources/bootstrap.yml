#默认公共配置
spring:
  jmx:
    enabled: false
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: dev1
      config:
        #nacos中心地址
        server-addr: 127.0.0.1:8848
        # 配置文件格式
        file-extension: yaml
        namespace: dev1
        override-none: true
        extension-configs:
          # 需共享的DataId，yaml后缀不能少，只支持yaml/properties
          # 越靠后，优先级越高
          - data-id: common.yaml
            refresh: true
  http:
    encoding:
      force: true
    charset: UTF-8
    enabled: true
  tomcat:
    uri-encoding: UTF-8
  cache:
    type: redis
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
#        dialect: org.hibernate.dialect.MySQLDialect
    #数据库生成策略，如果打开会根据entity对象生成数据库。尽量不要使用
    hibernate:
      ddl-auto: update
  #服务注册名
  application:
    name: pms
  #国际化配置
  messages:
    basename: i18n/messages
    cache-seconds: -1
    encoding: utf-8
mybatis:
  typeAliasesPackage: com.hvisions.pms.entitys
  mapperLocations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
server:
  tomcat:
    uri-encoding: utf-8
  port: 9080
ribbon:
  #请求处理的超时时间
  ReadTimeout: 120000
  #请求连接的超时时间
  ConnectTimeout: 30000

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
#info标签：可以在springboot admin的Insights界面Detail中进行展示
info:
  build:
    artifact: '@project.artifactId@'
    version: '@project.version@'
    server-name: ${h-visions.service-name}
my:
  variable: 1
  XCFlag: false
scheduled:
  materialCompleteness: 0/30 * * * * ?