<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.pms.dao.XcCutPlanReportMapper">
    <select id="getOneDataXcCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS finishCount
        FROM
            hv_pm_xc_material_cut_plan
        WHERE
            DATE(finish_time) = #{date}
            AND line_id =#{lineId}
    </select>

    <select id="getOneDataXcPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_xc_material_cut_plan
        WHERE
            DATE(send_time) = #{date}
            AND line_id =#{lineId}
    </select>

    <select id="getXcCutSevenDataByLineId" resultType="com.hvisions.pms.dto.HomeSevenDataDTO">
        SELECT
            COALESCE(SUM(CASE WHEN DATE(finish_time) = #{date} THEN 1 ELSE 0 END), 0) AS `today`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 1 DAY) THEN 1 ELSE 0 END), 0) AS `yesterday`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 2 DAY) THEN 1 ELSE 0 END), 0) AS `twoDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 3 DAY) THEN 1 ELSE 0 END), 0) AS `threeDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 4 DAY) THEN 1 ELSE 0 END), 0) AS `fourDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 5 DAY) THEN 1 ELSE 0 END), 0) AS `fiveDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(finish_time) = DATE_SUB(#{date}, INTERVAL 6 DAY) THEN 1 ELSE 0 END), 0) AS `sixDaysAgo`
        FROM hv_pm_xc_material_cut_plan
        WHERE
            line_id = #{lineId}  AND
            DATE(finish_time) BETWEEN DATE_SUB(#{date}, INTERVAL 6 DAY) AND #{date};
    </select>

    <select id="getOneWeekXcCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_xc_material_cut_plan
        WHERE
            DATE(finish_time) BETWEEN (
        -- 获取指定日期所在周的周一日期
            DATE_SUB(#{date}, INTERVAL DAYOFWEEK(#{date}) - 1 DAY)
            ) AND (
        -- 获取指定日期所在周的周日日期
            DATE_ADD(#{date}, INTERVAL 7 - DAYOFWEEK(#{date}) DAY)
            )
          AND line_id = #{lineId};
    </select>
    <select id="getOneMonthXcCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_xc_material_cut_plan
        WHERE
            YEAR(finish_time) = YEAR(#{date}) AND
            MONTH(finish_time) = MONTH(#{date}) AND
            line_id = #{lineId};
    </select>
    <select id="getOneYearXcCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_xc_material_cut_plan
        WHERE
            YEAR(finish_time) = YEAR(#{date}) AND
            line_id = #{lineId};
    </select>

    <select id="getOneWeekXcPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_xc_material_cut_plan
        WHERE
            DATE(send_time) BETWEEN (
        -- 获取指定日期所在周的周一日期
            DATE_SUB(#{date}, INTERVAL DAYOFWEEK(#{date}) - 1 DAY)
            ) AND (
        -- 获取指定日期所在周的周日日期
            DATE_ADD(#{date}, INTERVAL 7 - DAYOFWEEK(#{date}) DAY)
            )
          AND line_id = #{lineId};
    </select>
    <select id="getOneMonthXcPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_xc_material_cut_plan
        WHERE
            YEAR(send_time) = YEAR(#{date}) AND
            MONTH(send_time) = MONTH(#{date}) AND
            line_id = #{lineId};
    </select>
    <select id="getOneYearXcPlanCutByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(id) AS planCount
        FROM
            hv_pm_xc_material_cut_plan
        WHERE
            YEAR(send_time) = YEAR(#{date}) AND
            line_id = #{lineId};
    </select>





    <select id="getOneDataXcCutPartByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(p.id) AS finishCount
        FROM
            hv_pm_xc_material_cut_plan p
                LEFT JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
                LEFT JOIN hv_pm_xc_material_cut_plan_detail1 d1 ON d1.sub_plan_id = d0.id
        WHERE
            DATE ( p.finish_time ) = #{date}
          AND p.line_id = #{lineId}
    </select>

    <select id="getOneDataXcPlanCutPartByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT(p.id) AS planCount
        FROM
            hv_pm_xc_material_cut_plan p
                LEFT JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
                LEFT JOIN hv_pm_xc_material_cut_plan_detail1 d1 ON d1.sub_plan_id = d0.id
        WHERE
            DATE ( p.send_time ) = #{date}
          AND p.line_id = #{lineId}
    </select>

    <select id="getXcCutPartSevenDataByLineId" resultType="com.hvisions.pms.dto.HomeSevenDataDTO">
        SELECT
            COALESCE(SUM(CASE WHEN DATE(p.finish_time) = #{date} THEN 1 ELSE 0 END), 0) AS `today`,
            COALESCE(SUM(CASE WHEN DATE(p.finish_time) = DATE_SUB(#{date}, INTERVAL 1 DAY) THEN 1 ELSE 0 END), 0) AS `yesterday`,
            COALESCE(SUM(CASE WHEN DATE(p.finish_time) = DATE_SUB(#{date}, INTERVAL 2 DAY) THEN 1 ELSE 0 END), 0) AS `twoDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(p.finish_time) = DATE_SUB(#{date}, INTERVAL 3 DAY) THEN 1 ELSE 0 END), 0) AS `threeDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(p.finish_time) = DATE_SUB(#{date}, INTERVAL 4 DAY) THEN 1 ELSE 0 END), 0) AS `fourDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(p.finish_time) = DATE_SUB(#{date}, INTERVAL 5 DAY) THEN 1 ELSE 0 END), 0) AS `fiveDaysAgo`,
            COALESCE(SUM(CASE WHEN DATE(p.finish_time) = DATE_SUB(#{date}, INTERVAL 6 DAY) THEN 1 ELSE 0 END), 0) AS `sixDaysAgo`
        FROM
            hv_pm_xc_material_cut_plan p
            LEFT JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
            LEFT JOIN hv_pm_xc_material_cut_plan_detail1 d1 ON d1.sub_plan_id = d0.id
        WHERE
            p.line_id = #{lineId}  AND
            DATE(p.finish_time) BETWEEN DATE_SUB(#{date}, INTERVAL 6 DAY) AND #{date};
    </select>
    <select id="getOneWeekXcCutPartByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT( p.id ) AS planCount
        FROM
            hv_pm_xc_material_cut_plan p
                LEFT JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
                LEFT JOIN hv_pm_xc_material_cut_plan_detail1 d1 ON d1.sub_plan_id = d0.id
        WHERE
            DATE ( p.finish_time ) BETWEEN (-- 获取指定日期所在周的周一日期
            DATE_SUB( #{date}, INTERVAL DAYOFWEEK(#{date}) - 1 DAY)
            )
          AND (-- 获取指定日期所在周的周日日期
            DATE_ADD( #{date}, INTERVAL 7 - DAYOFWEEK(#{date}) DAY)
            )
          AND line_id = #{lineId};
    </select>
    <select id="getOneMonthXcCutPartByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT( p.id ) AS planCount
        FROM
            hv_pm_xc_material_cut_plan p
                LEFT JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
                LEFT JOIN hv_pm_xc_material_cut_plan_detail1 d1 ON d1.sub_plan_id = d0.id
        WHERE
            YEAR ( p.finish_time ) = YEAR (#{date}) AND
            MONTH ( p.finish_time ) = MONTH (#{date}) AND
            p.line_id = #{lineId};
    </select>
    <select id="getOneYearXcCutPartByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT( p.id ) AS planCount
        FROM
            hv_pm_xc_material_cut_plan p
                LEFT JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
                LEFT JOIN hv_pm_xc_material_cut_plan_detail1 d1 ON d1.sub_plan_id = d0.id
        WHERE
            YEAR ( p.finish_time ) = YEAR (#{date}) AND
            p.line_id = #{lineId};
    </select>
    <select id="getOneWeekXcPlanCutPartByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT( p.id ) AS planCount
        FROM
            hv_pm_xc_material_cut_plan p
                LEFT JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
                LEFT JOIN hv_pm_xc_material_cut_plan_detail1 d1 ON d1.sub_plan_id = d0.id
        WHERE
            DATE ( p.send_time ) BETWEEN (-- 获取指定日期所在周的周一日期
            DATE_SUB( #{date}, INTERVAL DAYOFWEEK(#{date}) - 1 DAY)
            )
          AND (-- 获取指定日期所在周的周日日期
            DATE_ADD( #{date}, INTERVAL 7 - DAYOFWEEK(#{date}) DAY)
            )
          AND line_id = #{lineId};
    </select>
    <select id="getOneMonthXcPlanCutPartByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT( p.id ) AS planCount
        FROM
            hv_pm_xc_material_cut_plan p
                LEFT JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
                LEFT JOIN hv_pm_xc_material_cut_plan_detail1 d1 ON d1.sub_plan_id = d0.id
        WHERE
            YEAR ( p.send_time ) = YEAR (#{date}) AND
            MONTH ( p.send_time ) = MONTH (#{date}) AND
            p.line_id = #{lineId};
    </select>
    <select id="getOneYearXcPlanCutPartByLineId" resultType="java.lang.Integer">
        SELECT
            COUNT( p.id ) AS planCount
        FROM
            hv_pm_xc_material_cut_plan p
                LEFT JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
                LEFT JOIN hv_pm_xc_material_cut_plan_detail1 d1 ON d1.sub_plan_id = d0.id
        WHERE
            YEAR ( p.send_time ) = YEAR (#{date}) AND
            p.line_id = #{lineId};
    </select>

    <select id="getCuttingCountDateRange" resultType="java.lang.Integer">
        SELECT COUNT(d0.id) <!-- 统计主表记录 -->
        FROM hv_pm_xc_material_cut_plan p
        INNER JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
        WHERE p.status = '6' <!-- 确认status字段类型 -->
        AND p.line_id = #{lineId}
        <choose>
            <when test="dateRange == 'last7Days'">
                AND p.finish_time &gt;= DATE_SUB(NOW(), INTERVAL 7 DAY)
            </when>
            <when test="dateRange == 'last30Days'">
                AND p.finish_time &gt;= DATE_SUB(NOW(), INTERVAL 30 DAY)
            </when>
            <when test="dateRange == 'currentWeek'">
                <!-- 使用WEEK模式1（周从周一开始） -->
                AND YEAR(p.finish_time) = YEAR(NOW())
                AND WEEK(p.finish_time, 1) = WEEK(NOW(), 1)
            </when>
            <when test="dateRange == 'currentMonth'">
                AND YEAR(p.finish_time) = YEAR(NOW())
                AND MONTH(p.finish_time) = MONTH(NOW())
            </when>
            <when test="dateRange == 'custom'">
                <!-- 使用处理后的 endDate（包含当天最后一刻） -->
                AND p.finish_time &gt;= #{startDate}
                AND p.finish_time &lt;= #{endDate}
            </when>
            <otherwise>
                <!-- 默认无时间限制 -->
            </otherwise>
        </choose>
    </select>

    <select id="getCuttingPlanCountDateRange" resultType="java.lang.Integer">
        SELECT COUNT(d0.id) <!-- 统计主表记录 -->
        FROM hv_pm_xc_material_cut_plan p
        INNER JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
        AND p.line_id = #{lineId}
        <choose>
            <when test="dateRange == 'last7Days'">
                AND p.send_time &gt;= DATE_SUB(NOW(), INTERVAL 7 DAY)
            </when>
            <when test="dateRange == 'last30Days'">
                AND p.send_time &gt;= DATE_SUB(NOW(), INTERVAL 30 DAY)
            </when>
            <when test="dateRange == 'currentWeek'">
                <!-- 使用WEEK模式1（周从周一开始） -->
                AND YEAR(p.send_time) = YEAR(NOW())
                AND WEEK(p.send_time, 1) = WEEK(NOW(), 1)
            </when>
            <when test="dateRange == 'currentMonth'">
                AND YEAR(p.send_time) = YEAR(NOW())
                AND MONTH(p.send_time) = MONTH(NOW())
            </when>
            <when test="dateRange == 'custom'">
                <!-- 使用处理后的 endDate（包含当天最后一刻） -->
                AND p.send_time &gt;= #{startDate}
                AND p.send_time &lt;= #{endDate}
            </when>
            <otherwise>
                <!-- 默认无时间限制 -->
            </otherwise>
        </choose>
    </select>


    <select id="getCuttingCountByDate" resultType="com.hvisions.pms.entity.CuttingCountResultDTO">
        SELECT
        DATE(p.finish_time) AS date,
        COUNT(d0.id) AS count
        FROM hv_pm_xc_material_cut_plan p
        INNER JOIN hv_pm_xc_material_cut_plan_detail0 d0 ON d0.order_id = p.ID
        WHERE p.status = '6'
        AND p.line_id = #{lineId}
        <choose>
            <when test="dateRange == 'last7Days'">
                AND p.finish_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            </when>
            <when test="dateRange == 'last15Days'">
                AND p.finish_time >= DATE_SUB(NOW(), INTERVAL 15 DAY)
            </when>
            <when test="dateRange == 'currentWeek'">
                <!-- 使用WEEK模式1（周从周一开始） -->
                AND YEAR(p.finish_time) = YEAR(NOW())
                AND WEEK(p.finish_time, 1) = WEEK(NOW(), 1)
            </when>
            <when test="dateRange == 'currentMonth'">
                AND YEAR(p.finish_time) = YEAR(NOW())
                AND MONTH(p.finish_time) = MONTH(NOW())
            </when>
            <when test="dateRange == 'custom'">
                <!-- 使用处理后的 endDate（包含当天最后一刻） -->
                AND p.finish_time &gt;= #{startDate}
                AND p.finish_time &lt;= #{endDate}
            </when>
            <otherwise>
                -- 默认无时间限制
            </otherwise>
        </choose>
        GROUP BY DATE(p.finish_time)
        ORDER BY DATE(p.finish_time);
    </select>

</mapper>